﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstInspection
{
    public int InspectionId { get; set; }

    public int? FacilityId { get; set; }

    public string? Title { get; set; }

    public string? Description { get; set; }

    public DateTime InspectionDate { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public string? ReferenceNo { get; set; }

    public int? TypeOfInspection { get; set; }

    public string? InspectionLocation { get; set; }

    public virtual MstFacility? Facility { get; set; }

    public virtual ICollection<MstInspectionItem> MstInspectionItems { get; set; } = new List<MstInspectionItem>();
}
