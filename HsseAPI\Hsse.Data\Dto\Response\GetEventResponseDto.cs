﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class GetEventResponseDto
    {
        public int EventId { get; set; }

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public string? MediaUrl { get; set; }

        public DateTime? EventDateTime { get; set; }

        public string? Location { get; set; }

        public string? ExternalLink { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? CreatedBy { get; set; }

        public int? FacilityId { get; set; }

        public bool IsActive { get; set; }

        public DateTime? ScheduleAt { get; set; }

        public DateTime? ExpiryAt { get; set; }

        public bool IsRsvp { get; set; }
        public EventResponseResponseDto eventResponseResponseDto { get; set; } = null!;
    }
    public class EventResponseResponseDto
    {
        public int ResponseId { get; set; }

        public int EventId { get; set; }

        public int UserId { get; set; }

        public bool? IsAccepted { get; set; }

        public DateTime? RespondedAt { get; set; }
    }
}
