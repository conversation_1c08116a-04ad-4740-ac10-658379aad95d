﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface ILoginRepository
    {
        LoginResponseDto ValidateUser(LoginRequestDto loginRequestDto);
        List<MstLanguage> GetLanguages();
        DeviceRequestDto AppInformation(DeviceRequestDto deviceDetail);
    }
}
