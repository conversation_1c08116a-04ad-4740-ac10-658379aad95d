﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Data.Attributes;

namespace Hsse.Data.Dto.Request
{
    public class LoginRequestDto
    {
        [EmailValidation(ErrorMessage = "Employee email must be a valid email address")]
        [StringLength(100, ErrorMessage = "Employee email cannot exceed 100 characters")]
        public string? employeeEmail { get; set; } = null;

        [StringLength(50, ErrorMessage = "Employee ID cannot exceed 50 characters")]
        public string? employeeId { get; set; } = null;

        [Range(1, int.MaxValue, ErrorMessage = "Language ID must be a positive number")]
        public int? langId { get; set; } = null;
    }
}
