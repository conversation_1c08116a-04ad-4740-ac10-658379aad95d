﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class InspectionService : IInspectionService
    {
        private readonly IInspectionRepository _IInspectionRepository;

        public InspectionService(IInspectionRepository inspectionRepository)
        {
            _IInspectionRepository = inspectionRepository;
        }

        public long CreateOrUpdateInspection(MstInspectionDto createInspectionDto)
        {
            var result = _IInspectionRepository.CreateOrUpdateInspection(createInspectionDto);
            return result;
        }

        public int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto)
        {
            var result = _IInspectionRepository.VerifyActionParty(verifyActionPartyDto);
            return result;
        }

        public int VerifyInspector(VerifyInspectorDto verifyInspectorDto)
        {
            var result = _IInspectionRepository.VerifyInspector(verifyInspectorDto);
            return result;
        }

        public List<InspectionVerifiedDto> GetInspectionsByVerifiedOrNot(int verificationStatus)
        {
            var result = _IInspectionRepository.GetInspectionsByVerifiedOrNot(verificationStatus);
            return result;
        }

        public List<MstActionParty> GetActionParties()
        {
            var result = _IInspectionRepository.GetActionParties();
            return result;
        }
        public List<MstInspectionCategory> GetInspectionCategory()
        {
            var result = _IInspectionRepository.GetInspectionCategory();
            return result;
        }
        public List<InspectionVerifiedDto> GetInspections(int? inspectionId)
        {
            var result = _IInspectionRepository.GetInspections(inspectionId);
            return result;
        }

    }
}
