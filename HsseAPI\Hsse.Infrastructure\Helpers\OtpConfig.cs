﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Helpers
{
    public static class OtpConfig
    {
        public static int OtpLength => 6;
        public static int RetryCount => 3;
        public static TimeSpan ExpireDuration => TimeSpan.FromMinutes(5);
        public static string MessageId => "OTP_MSG";
        public static string Pwd => "default_pwd";
        public static string Parameter => "default_param";
        public static string Provider => ""; // Empty for development - configure with real SMS provider URL in production
    }
}
