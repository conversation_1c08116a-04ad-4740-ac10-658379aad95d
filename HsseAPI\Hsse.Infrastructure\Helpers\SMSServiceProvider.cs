﻿using Hsse.Data.Dto.Request;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Helpers
{
    internal class SMSServiceProvider
    {
        private static ILogger? _logger;


        public static void SetLogger(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Sends an OTP asynchronously.
        /// </summary>
        /// <param name="data">The data containing the OTP and other details.</param>
        /// <returns>A boolean indicating whether the OTP was sent successfully.</returns>
        public static async Task<bool> SendOtpAsync(OtpData data)
        {
            var msg = $"Store Inventory OTP is: {data.OTP}";
            var phone = $"+65{data.PhoneNumber.Trim('+')}";

            var dicQueries = new Dictionary<string, object>
            {
                { "to", phone },
                { "msg", msg },
                { "pwd", data.Pwd },
                { "parameter", data.Parameter },
                { "messageID", data.MessageId },
            };

            var query = UrlUtilities.ConstructQueryString(dicQueries, false);

            try
            {
                _logger?.LogInformation("Sending OTP to {Phone} with message: {Message}", phone, msg);
                var ret = await GetResponse(query, data.Provider);
                if (ret)
                {
                    _logger?.LogInformation("OTP sent successfully.");
                }
                else
                {
                    _logger?.LogWarning("Failed to send OTP.");
                }
                return ret;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while sending OTP.");
                return false;
            }
        }

        /// <summary>
        /// Gets the response from the SMS provider.
        /// </summary>
        /// <param name="query">The query string to be sent.</param>
        /// <param name="url">The URL of the SMS provider.</param>
        /// <returns>A boolean indicating whether the request was successful.</returns>
        private static async Task<bool> GetResponse(string query, string url)
        {
            try
            {
                // Check if SMS provider URL is configured
                if (string.IsNullOrWhiteSpace(url))
                {
                    _logger?.LogWarning("SMS provider URL is not configured. Simulating successful SMS send for development/testing.");
                    return true; // Simulate success for development/testing
                }

                using (var hc = new HttpClient())
                {
                    // Set the timeout for the HttpClient
                    hc.Timeout = TimeSpan.FromSeconds(30); // Adjust as needed

                    // Clear and set the default request headers
                    hc.DefaultRequestHeaders.Accept.Clear();
                    hc.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));

                    // Prepare the content
                    var content = new StringContent(query, Encoding.UTF8, "application/x-www-form-urlencoded");

                    // Log the start of the request
                    _logger?.LogInformation("Starting HTTP request to SMS provider: {Url}", url);

                    // Send the POST request
                    var response = await hc.PostAsync(url, content);

                    // Log the response time
                    _logger?.LogInformation("HTTP request completed with status code: {StatusCode}", response.StatusCode);

                    // Check if the response was successful
                    if (response.IsSuccessStatusCode)
                    {
                        _logger?.LogInformation("Received successful response from SMS provider.");
                    }
                    else
                    {
                        _logger?.LogWarning("Failed response from SMS provider: {StatusCode}", response.StatusCode);
                    }

                    // Return whether the response was successful
                    return response.IsSuccessStatusCode;
                }
            }
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP request specific exceptions
                _logger?.LogError(httpEx, "HTTP request error occurred while getting response from SMS provider.");
                return false;
            }
            catch (TaskCanceledException timeoutEx)
            {
                // Handle timeout exceptions
                _logger?.LogError(timeoutEx, "HTTP request timed out while getting response from SMS provider.");
                return false;
            }
            catch (Exception ex)
            {
                // Handle all other exceptions
                _logger?.LogError(ex, "An error occurred while getting response from SMS provider.");
                return false;
            }
        }
    }
}
