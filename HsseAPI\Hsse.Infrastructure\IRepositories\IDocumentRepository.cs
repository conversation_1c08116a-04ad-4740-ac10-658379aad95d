using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IDocumentRepository
    {
        List<MstDocumentLibrary> GetAllDocuments();
        List<MstDocumentLibrary> GetDocumentsByFacility(int facilityId);
        List<string> GetDocumentCategories();
        List<MstDocumentLibrary> GetDocumentsByCategory(string category);
        List<MstDocumentLibrary> GetDocumentsByCategoryAndFacility(string category, int facilityId);
        MstDocumentLibrary GetDocumentById(int documentId);
    }
}
