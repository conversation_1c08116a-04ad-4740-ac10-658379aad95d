﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstFollowupPost
{
    public int FollowupId { get; set; }

    public int PostId { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? Format { get; set; }

    public string? AssignedTo { get; set; }

    public DateTime? AssignedTime { get; set; }

    public int? FollowedupBy { get; set; }

    public DateTime? FollowedupTime { get; set; }

    public int? CompletedBy { get; set; }

    public DateTime? CompletedTime { get; set; }

    public int? ArchivedBy { get; set; }

    public DateTime? ArchivedTime { get; set; }

    public string? Comments { get; set; }

    public virtual MstPost Post { get; set; } = null!;
}
