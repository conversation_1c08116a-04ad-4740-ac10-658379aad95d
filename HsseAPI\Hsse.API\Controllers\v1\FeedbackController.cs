using Asp.Versioning;
using Hsse.Application.Helper;
using Hsse.Application.IServices;
using Hsse.Application.Services;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [Authorize]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _IFeedbackService;
        private readonly ILogger<FeedbackController> _logger;
        private readonly EncryptionHelper _encryptionHelper;

        public FeedbackController(IFeedbackService feedbackService, EncryptionHelper encryptionHelper, IConfiguration configuration, ILogger<FeedbackController> logger)
        {
            _logger = logger;
            _IFeedbackService = feedbackService;
            _encryptionHelper = encryptionHelper;
        }
        [HttpPost("CreateOrUpdateFeedback")]
        public IActionResult CreateOrUpdateFeedback([FromBody] FeedbackRequestDto dto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IFeedbackService.CreateOrUpdateFeedback(dto);
                response.Status = 1;
                response.Message = "feedback operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing event operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

    }
}
