﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstAnnouncementReceiver
{
    public int ReceiverId { get; set; }

    public int? AnnouncementId { get; set; }

    public int? UserId { get; set; }

    public int? GroupId { get; set; }

    public bool? Delivered { get; set; }

    public int? EventId { get; set; }

    public virtual MstAnnouncement? Announcement { get; set; }

    public virtual MstEvent? Event { get; set; }

    public virtual MstGroup? Group { get; set; }

    public virtual MstUser? User { get; set; }
}
