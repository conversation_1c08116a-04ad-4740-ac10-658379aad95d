﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dapper.SqlMapper;

namespace Hsse.Infrastructure.Repositories
{
    public class InspectionRepository : IInspectionRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly IStorageRepository _storageRepository;

        public InspectionRepository(MasterDBContext masterDBContext, IStorageRepository storageRepository)
        {
            _MasterDBContext = masterDBContext;
            _storageRepository = storageRepository;
        }

        public long CreateOrUpdateInspection(MstInspectionDto createInspectionDto)
        {
            // Upload all attachments for each inspection item
            foreach (var item in createInspectionDto.InspectionItems)
            {
                if (!string.IsNullOrEmpty(item.ObservationAttachmentBase64))
                {
                    item.ObservationAttachmentName = Guid.NewGuid() + ".jpeg";
                    _storageRepository.UploadBlob(item.ObservationAttachmentBase64, item.ObservationAttachmentName, "inspection-media");
                }

                if (!string.IsNullOrEmpty(item.RecommendationAttachmentBase64))
                {
                    item.RecommendationAttachmentName = Guid.NewGuid() + ".jpeg";
                    _storageRepository.UploadBlob(item.RecommendationAttachmentBase64, item.RecommendationAttachmentName, "inspection-media");
                }
            }

            if (createInspectionDto.InspectionId == 0)
            {
                // Create new inspection
                var entity = new MstInspection
                {
                    FacilityId = createInspectionDto.FacilityId,
                    Title = createInspectionDto.Title,
                    Description = createInspectionDto.Description,
                    InspectionDate = createInspectionDto.InspectionDate ?? DateTime.UtcNow,
                    ReferenceNo = createInspectionDto.ReferenceNo,
                    CreatedBy = createInspectionDto.CreatedBy,
                    CreatedAt = DateTime.UtcNow,
                    InspectionLocation = createInspectionDto.SpecificLocation,
                    ModifiedAt = null
                };

                _MasterDBContext.MstInspections.Add(entity);
                _MasterDBContext.SaveChanges();

                // Add all inspection items
                foreach (var item in createInspectionDto.InspectionItems)
                {
                    var inspectionItem = new MstInspectionItem
                    {
                        InspectionId = entity.InspectionId,
                        SpecificLocation = item.SpecificLocation,
                        Description = item.Description,
                        ActionPartyId = item.ActionPartyId?.ToString(),
                        Observation = item.Observation,
                        Recommendation = item.Recommendation,
                        ContactPersonId = item.ContactPersonId,
                        Verification = null,
                        Status = null,
                        ObservationMediaUrl = !string.IsNullOrEmpty(item.ObservationAttachmentName)
                            ? $"https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/{item.ObservationAttachmentName}"
                            : null,
                        RecommendationMediaUrl = !string.IsNullOrEmpty(item.RecommendationAttachmentName)
                            ? $"https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/{item.RecommendationAttachmentName}"
                            : null,
                        AfterImagePath = null,
                        Rectification = null,
                        CreatedAt = DateTime.UtcNow
                    };

                    _MasterDBContext.MstInspectionItems.Add(inspectionItem);
                }

                _MasterDBContext.SaveChanges();
                return 1;
            }
            else
            {
                // Update existing inspection
                var entity = _MasterDBContext.MstInspections
                    .Include(x => x.MstInspectionItems)
                    .FirstOrDefault(x => x.InspectionId == createInspectionDto.InspectionId);

                if (entity == null)
                    return 0;

                entity.FacilityId = createInspectionDto.FacilityId;
                entity.Title = createInspectionDto.Title;
                entity.Description = createInspectionDto.Description;
                entity.ModifiedAt = DateTime.UtcNow;

                // Update or add inspection items
                foreach (var item in createInspectionDto.InspectionItems)
                {
                    MstInspectionItem existingItem = null;

                    if (item.ItemId.HasValue)
                    {
                        existingItem = entity.MstInspectionItems.FirstOrDefault(x => x.ItemId == item.ItemId);
                    }

                    if (existingItem != null)
                    {
                        // Update existing item
                        existingItem.Description = item.Description;
                        existingItem.ActionPartyId = item.ActionPartyId?.ToString();
                        existingItem.ContactPersonId = item.ContactPersonId;
                        existingItem.SpecificLocation = item.SpecificLocation;
                        existingItem.Observation = item.Observation;
                        existingItem.Recommendation = item.Recommendation;
                        existingItem.ModifiedAt = DateTime.UtcNow;

                        if (!string.IsNullOrEmpty(item.ObservationAttachmentName))
                            existingItem.ObservationMediaUrl = $"https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/{item.ObservationAttachmentName}";

                        if (!string.IsNullOrEmpty(item.RecommendationAttachmentName))
                            existingItem.RecommendationMediaUrl = $"https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/{item.RecommendationAttachmentName}";
                    }
                    else
                    {
                        // Add new item
                        var newItem = new MstInspectionItem
                        {
                            InspectionId = entity.InspectionId,
                            SpecificLocation = item.SpecificLocation,
                            Description = item.Description,
                            ActionPartyId = item.ActionPartyId?.ToString(),
                            Observation = item.Observation,
                            Recommendation = item.Recommendation,
                            ContactPersonId = item.ContactPersonId,
                            CreatedAt = DateTime.UtcNow,
                            ObservationMediaUrl = !string.IsNullOrEmpty(item.ObservationAttachmentName)
                                ? $"https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/{item.ObservationAttachmentName}"
                                : null,
                            RecommendationMediaUrl = !string.IsNullOrEmpty(item.RecommendationAttachmentName)
                                ? $"https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/{item.RecommendationAttachmentName}"
                                : null
                        };

                        _MasterDBContext.MstInspectionItems.Add(newItem);
                    }
                }

                _MasterDBContext.SaveChanges();
                return 2;
            }
        }


        public int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto)
        {
            if (!string.IsNullOrEmpty(verifyActionPartyDto.AfterImageBase64))
            {
                verifyActionPartyDto.AfterImageFileName = Guid.NewGuid().ToString() + ".jpeg";
                _storageRepository.UploadBlob(verifyActionPartyDto.AfterImageBase64, verifyActionPartyDto.AfterImageFileName, "inspection-media");
            }
            var inspectionItem = _MasterDBContext.MstInspectionItems
                .FirstOrDefault(i => i.ItemId == verifyActionPartyDto.ItemId);

            if (inspectionItem == null)
                return 0; // Item not found

            // Store old values for audit
            var oldActionParty = inspectionItem.ActionPartyId?.ToString();
            var oldVerification = inspectionItem.Verification?.ToString();

            inspectionItem.ActionPartyId = verifyActionPartyDto.ActionPartyId.ToString();
            inspectionItem.Verification = 1; // Assuming 1 means verified
            inspectionItem.ModifiedAt = DateTime.Now;
            inspectionItem.AfterImagePath = "https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/" + verifyActionPartyDto.AfterImageFileName;
            inspectionItem.Description = verifyActionPartyDto.Description;

            _MasterDBContext.MstInspectionItems.Attach(inspectionItem);
            _MasterDBContext.SaveChanges();

            // Add audit log using the correct entity structure
            var auditLog = new MstInspectionItemAudit
            {
                ItemId = verifyActionPartyDto.ItemId,
                OldStatus = oldVerification,
                NewStatus = "1", // Verified
                ChangedBy = verifyActionPartyDto.VerifiedBy,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstInspectionItemAudits.Add(auditLog);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int VerifyInspector(VerifyInspectorDto verifyInspectorDto)
        {
            if (!string.IsNullOrEmpty(verifyInspectorDto.RecomendationImageBase64))
            {
                verifyInspectorDto.RecomendationImageFileName = Guid.NewGuid().ToString() + ".jpeg";
                _storageRepository.UploadBlob(verifyInspectorDto.RecomendationImageBase64, verifyInspectorDto.RecomendationImageFileName, "inspection-media");
            }
            var inspectionItem = _MasterDBContext.MstInspectionItems
                .FirstOrDefault(i => i.ItemId == verifyInspectorDto.ItemId);

            if (inspectionItem == null)
                return 0; // Item not found

            inspectionItem.ModifiedAt = DateTime.Now;
            inspectionItem.RecommendationMediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/" + verifyInspectorDto.RecomendationImageFileName;
            inspectionItem.Recommendation = verifyInspectorDto.Description;

            _MasterDBContext.MstInspectionItems.Attach(inspectionItem);
            _MasterDBContext.SaveChanges();

            // For inspector verification, we'll use the general audit log table
            // since MstInspectionItemAudit is specifically for inspection items
            var auditLog = new MstAuditLog
            {
                TableName = "MstInspections",
                RecordPrimaryKey = verifyInspectorDto.InspectionId,
                OperationType = "Inspector Verification",
                OldValues = $"Inspector: Not Verified",
                NewValues = $"Inspector: {verifyInspectorDto.InspectorId}, Verified: {verifyInspectorDto.IsVerified}",
                ChangedBy = verifyInspectorDto.InspectorId,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstAuditLogs.Add(auditLog);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public List<InspectionVerifiedDto> GetInspectionsByVerifiedOrNot(int verificationStatus)
        {

            var inspectionsWithVerifiedItems = _MasterDBContext.MstInspections
                .Where(i => i.MstInspectionItems.Any(ii => ii.Verification == verificationStatus))
                .Select(i => new InspectionVerifiedDto
                {
                    InspectionId = i.InspectionId,
                    FacilityId = i.FacilityId,
                    Title = i.Title,
                    Description = i.Description,
                    InspectionDate = i.InspectionDate,
                    CreatedBy = i.CreatedBy,
                    CreatedAt = i.CreatedAt,
                    ModifiedAt = i.ModifiedAt,
                    ReferenceNo = i.ReferenceNo,
                    TypeOfInspection = i.TypeOfInspection,
                    InspectionLocation = i.InspectionLocation,
                    InpectionItemDtos = i.MstInspectionItems
                        .Where(ii => ii.Verification == verificationStatus)
                        .Select(ii => new InpectionItemDto
                        {
                            ItemId = ii.ItemId,
                            InspectionId = ii.InspectionId,
                            Description = ii.Description,
                            SpecificLocation = ii.SpecificLocation,
                            Recommendation = ii.Recommendation,
                            Status = ii.Status,
                            Rectification = ii.Rectification,
                            AfterImagePath = ii.AfterImagePath,
                            CompletionDateTime = ii.CompletionDateTime,
                            CreatedAt = ii.CreatedAt,
                            ModifiedAt = ii.ModifiedAt,
                            RecommendationMediaUrl = ii.RecommendationMediaUrl,
                            Observation = ii.Observation,
                            ObservationMediaUrl = ii.ObservationMediaUrl,
                            Verification = ii.Verification,
                            ContactPersonId = ii.ContactPersonId,
                            Location = ii.Location,
                            ObservationType = ii.ObservationType,
                            ActionPartyId = ii.ActionPartyId,
                            CreatedBy = ii.CreatedBy
                        }).ToList()
                })
                .ToList();

            return inspectionsWithVerifiedItems;
        }

        public List<MstActionParty> GetActionParties()
        {
            return _MasterDBContext.MstActionParties.ToList();
        }
        public List<MstInspectionCategory> GetInspectionCategory()
        {
            var result = _MasterDBContext.MstInspectionCategories.ToList();
            return result;
        }
        public List<InspectionVerifiedDto> GetInspections(int? inspectionId)
        {
            var inspectionsWithVerifiedItems = _MasterDBContext.MstInspections
                .Where(i => (!inspectionId.HasValue || i.InspectionId == inspectionId)
                            && i.MstInspectionItems.Any())
                .Select(i => new InspectionVerifiedDto
                {
                    InspectionId = i.InspectionId,
                    FacilityId = i.FacilityId,
                    Title = i.Title,
                    Description = i.Description,
                    InspectionDate = i.InspectionDate,
                    CreatedBy = i.CreatedBy,
                    CreatedAt = i.CreatedAt,
                    ModifiedAt = i.ModifiedAt,
                    ReferenceNo = i.ReferenceNo,
                    TypeOfInspection = i.TypeOfInspection,
                    InspectionLocation = i.InspectionLocation,
                    InpectionItemDtos = i.MstInspectionItems
                        .Select(ii => new InpectionItemDto
                        {
                            ItemId = ii.ItemId,
                            InspectionId = ii.InspectionId,
                            Description = ii.Description,
                            SpecificLocation = ii.SpecificLocation,
                            Recommendation = ii.Recommendation,
                            Status = ii.Status,
                            Rectification = ii.Rectification,
                            AfterImagePath = "https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/" + ii.AfterImagePath,
                            CompletionDateTime = ii.CompletionDateTime,
                            CreatedAt = ii.CreatedAt,
                            ModifiedAt = ii.ModifiedAt,
                            RecommendationMediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/" + ii.RecommendationMediaUrl,
                            Observation = ii.Observation,
                            ObservationMediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/inspection-media/" + ii.ObservationMediaUrl,
                            Verification = ii.Verification,
                            ContactPersonId = ii.ContactPersonId,
                            Location = ii.Location,
                            ObservationType = ii.ObservationType,
                            ActionPartyId = ii.ActionPartyId,
                            CreatedBy = ii.CreatedBy
                        }).ToList()
                })
                .ToList();

            return inspectionsWithVerifiedItems;
        }

    }
}
