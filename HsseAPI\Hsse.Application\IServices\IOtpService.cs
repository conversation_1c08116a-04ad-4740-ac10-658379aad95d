﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface IOtpService
    {
        Task<OtpTriggerResponseDto> TriggerOtpAsync(OtpTriggeredRQ request);
        Task<OtpResendResponseDto> ResendOtpAsync(ResendOtpRQ request);
        Task<OtpValidationResponseDto> ValidateOtpAsync(OtpSubmitRQ request);

        // Synchronous versions for backward compatibility
        OtpTriggerResponseDto TriggerOtp(OtpTriggeredRQ request);
        OtpResendResponseDto ResendOtp(ResendOtpRQ request);
        OtpValidationResponseDto ValidateOtp(OtpSubmitRQ request);
    }
}
