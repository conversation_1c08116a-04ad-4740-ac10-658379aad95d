﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class InspectionVerifiedDto
    {
        public int InspectionId { get; set; }

        public int? FacilityId { get; set; }

        public string? Title { get; set; }

        public string? Description { get; set; }

        public DateTime InspectionDate { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public string? ReferenceNo { get; set; }

        public int? TypeOfInspection { get; set; }

        public string? InspectionLocation { get; set; }
        public List<InpectionItemDto> InpectionItemDtos { get; set; } = [];
    }
    public class InpectionItemDto
    {
        public int ItemId { get; set; }

        public int InspectionId { get; set; }

        public string? Description { get; set; }

        public string? SpecificLocation { get; set; }

        public string? Recommendation { get; set; }

        public int? Status { get; set; }

        public string? Rectification { get; set; }

        public string? AfterImagePath { get; set; }

        public DateTime? CompletionDateTime { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public string? RecommendationMediaUrl { get; set; }

        public string? Observation { get; set; }

        public string? ObservationMediaUrl { get; set; }

        public int? Verification { get; set; }

        public int? ContactPersonId { get; set; }

        public string? Location { get; set; }

        public int? ObservationType { get; set; }

        public string? ActionPartyId { get; set; }

        public int? CreatedBy { get; set; }
    }
}
