﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Data.Attributes;

namespace Hsse.Data.Dto.Request
{
    public class OtpTriggeredRQ
    {
        [Required(ErrorMessage = "Employee Name ID is required")]
        [NotEmpty(ErrorMessage = "Employee Name ID cannot be empty")]
        [StringLength(50, ErrorMessage = "Employee Name ID cannot exceed 50 characters")]
        public string EmpNameId { get; set; } = string.Empty;
    }

    public class ResendOtpRQ
    {
        [Required(ErrorMessage = "Employee Name ID is required")]
        [NotEmpty(ErrorMessage = "Employee Name ID cannot be empty")]
        [StringLength(50, ErrorMessage = "Employee Name ID cannot exceed 50 characters")]
        public string EmpNameId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Reference ID is required")]
        [GuidValidation(ErrorMessage = "Reference ID must be a valid GUID")]
        public Guid ReferenceId { get; set; }
    }

    public class OtpSubmitRQ
    {
        [Required(ErrorMessage = "Employee Name ID is required")]
        [NotEmpty(ErrorMessage = "Employee Name ID cannot be empty")]
        [StringLength(50, ErrorMessage = "Employee Name ID cannot exceed 50 characters")]
        public string EmpNameId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Reference ID is required")]
        [GuidValidation(ErrorMessage = "Reference ID must be a valid GUID")]
        public Guid ReferenceId { get; set; }

        [Required(ErrorMessage = "OTP is required")]
        [NotEmpty(ErrorMessage = "OTP cannot be empty")]
        [StringLength(6, MinimumLength = 6, ErrorMessage = "OTP must be exactly 6 characters")]
        [RegularExpression(@"^\d{6}$", ErrorMessage = "OTP must be a 6-digit number")]
        public string Otp { get; set; } = string.Empty;
    }
}
