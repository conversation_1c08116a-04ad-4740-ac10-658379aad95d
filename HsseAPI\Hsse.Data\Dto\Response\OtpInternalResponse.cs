﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class OtpResponse
    {
        public Guid ReferenceId { get; set; }
        public int RetryCount { get; set; }
        public int OtpLength { get; set; }
        public DateTime ExpiresAtDate { get; set; }
        public long ExpiresAt { get; set; }
    }

    public class OtpSubmitResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int RetryCount { get; set; }
    }
}
