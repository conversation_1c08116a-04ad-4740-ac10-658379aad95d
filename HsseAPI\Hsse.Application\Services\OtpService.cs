﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class OtpService : IOtpService
    {
        private readonly IOtpRepository _otpRepository;

        public OtpService(IOtpRepository otpRepository)
        {
            _otpRepository = otpRepository;
        }

        public async Task<OtpTriggerResponseDto> TriggerOtpAsync(OtpTriggeredRQ request)
        {
            try
            {
                return await _otpRepository.TriggerOtpAsync(request.EmpNameId);
            }
            catch (Exception)
            {
                return new OtpTriggerResponseDto
                {
                    Status = 0,
                    Message = "Failed to trigger OTP"
                };
            }
        }

        public async Task<OtpResendResponseDto> ResendOtpAsync(ResendOtpRQ request)
        {
            try
            {
                return await _otpRepository.ResendOtpAsync(request.EmpNameId, request.ReferenceId);
            }
            catch (Exception)
            {
                return new OtpResendResponseDto
                {
                    Status = 0,
                    Message = "Failed to resend OTP"
                };
            }
        }

        public async Task<OtpValidationResponseDto> ValidateOtpAsync(OtpSubmitRQ request)
        {
            try
            {
                return await _otpRepository.ValidateOtpAsync(request.EmpNameId, request.ReferenceId, request.Otp);
            }
            catch (Exception)
            {
                return new OtpValidationResponseDto
                {
                    Status = 0,
                    Message = "Failed to validate OTP",
                    IsValid = false
                };
            }
        }



        // Synchronous versions for backward compatibility
        public OtpTriggerResponseDto TriggerOtp(OtpTriggeredRQ request)
        {
            return TriggerOtpAsync(request).GetAwaiter().GetResult();
        }

        public OtpResendResponseDto ResendOtp(ResendOtpRQ request)
        {
            return ResendOtpAsync(request).GetAwaiter().GetResult();
        }

        public OtpValidationResponseDto ValidateOtp(OtpSubmitRQ request)
        {
            return ValidateOtpAsync(request).GetAwaiter().GetResult();
        }
    }
}
