using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class FeedbackRepository : IFeedbackRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly IStorageRepository _storageRepository;
        public FeedbackRepository(MasterDBContext masterDBContext, IStorageRepository storageRepository)
        {
            _MasterDBContext = masterDBContext;
            _storageRepository = storageRepository;

        }
        public int CreateOrUpdateFeedback(FeedbackRequestDto dto)
        {
            try
            {
                if (!string.IsNullOrEmpty(dto.ImageBase64))
                {
                    dto.FileName = Guid.NewGuid().ToString() + ".jpeg";
                    _storageRepository.UploadBlob(dto.ImageBase64, dto.FileName, "feedback-media");
                }
                // Placeholder for the actual implementation
                if (dto.FeedbackId > 0)
                {
                    // Update existing feedback
                    var existingFeedback = _MasterDBContext.MstFeedbacks.FirstOrDefault(f => f.FeedbackId == dto.FeedbackId);
                    if (existingFeedback != null)
                    {
                        existingFeedback.Name = dto.Name;
                        existingFeedback.Title = dto.Title;
                        existingFeedback.Description = dto.Description;
                        existingFeedback.Response = dto.Response;
                        existingFeedback.FilePath = "https://uetrackstorage.blob.core.windows.net/hssev2/feedback-media/" + dto.FileName;
                        existingFeedback.Status = dto.Status;
                        existingFeedback.UpdatedBy = dto.UpdatedBy;
                        existingFeedback.CreatedAt = DateTime.UtcNow;
                        existingFeedback.FacilityId = dto.FacilityId;
                        existingFeedback.Date = DateTime.UtcNow;
                        _MasterDBContext.MstFeedbacks.Attach(existingFeedback);
                        _MasterDBContext.SaveChanges();

                        return 2;
                    }
                    else
                    {
                        throw new Exception("Feedback not found.");
                    }
                }
                else
                {
                    // Create new feedback
                    var newFeedback = new MstFeedback
                    {
                        Name = dto.Name,
                        Title = dto.Title,
                        Description = dto.Description,
                        Response = dto.Response,
                        FilePath = dto.FileName,
                        Status = dto.Status,
                        CreatedBy = dto.CreatedBy,
                        CreatedAt = DateTime.UtcNow,
                        FacilityId = dto.FacilityId,
                        Date = DateTime.UtcNow
                    };
                    _MasterDBContext.MstFeedbacks.Add(newFeedback);
                    _MasterDBContext.SaveChanges();
                    return 1;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
