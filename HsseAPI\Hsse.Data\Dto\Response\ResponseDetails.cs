﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class ResponseDetails
    {
        public int Status { get; set; }
        public string? Message { get; set; }
        public dynamic? Result { get; set; }
    }

    public class ValidationErrorResponse : ResponseDetails
    {
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();

        public ValidationErrorResponse()
        {
            Status = 0;
            Message = "Validation failed";
        }
    }

    public class ValidationError
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public object? AttemptedValue { get; set; }
    }
}
