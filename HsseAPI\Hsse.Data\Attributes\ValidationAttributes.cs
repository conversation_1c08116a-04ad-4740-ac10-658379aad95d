using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Hsse.Data.Attributes
{
    /// <summary>
    /// Validates that a string is not null, empty, or whitespace
    /// </summary>
    public class NotEmptyAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is string stringValue)
            {
                return !string.IsNullOrWhiteSpace(stringValue);
            }
            return value != null;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} cannot be empty or whitespace.";
        }
    }

    /// <summary>
    /// Validates email format
    /// </summary>
    public class EmailValidationAttribute : ValidationAttribute
    {
        private static readonly Regex EmailRegex = new Regex(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
                return true; // Allow null/empty, use [Required] for mandatory fields

            return EmailRegex.IsMatch(value.ToString()!);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} must be a valid email address.";
        }
    }

    /// <summary>
    /// Validates phone number format
    /// </summary>
    public class PhoneValidationAttribute : ValidationAttribute
    {
        private static readonly Regex PhoneRegex = new Regex(
            @"^[\+]?[1-9][\d]{0,15}$",
            RegexOptions.Compiled);

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
                return true; // Allow null/empty, use [Required] for mandatory fields

            return PhoneRegex.IsMatch(value.ToString()!);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} must be a valid phone number.";
        }
    }

    /// <summary>
    /// Validates that a number is positive
    /// </summary>
    public class PositiveNumberAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null)
                return true; // Allow null, use [Required] for mandatory fields

            if (value is int intValue)
                return intValue > 0;
            if (value is long longValue)
                return longValue > 0;
            if (value is decimal decimalValue)
                return decimalValue > 0;
            if (value is double doubleValue)
                return doubleValue > 0;
            if (value is float floatValue)
                return floatValue > 0;

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} must be a positive number.";
        }
    }

    /// <summary>
    /// Validates Base64 string format
    /// </summary>
    public class Base64ValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
                return true; // Allow null/empty, use [Required] for mandatory fields

            try
            {
                Convert.FromBase64String(value.ToString()!);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} must be a valid Base64 string.";
        }
    }

    /// <summary>
    /// Validates GUID format
    /// </summary>
    public class GuidValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null)
                return true; // Allow null, use [Required] for mandatory fields

            if (value is Guid)
                return true;

            if (value is string stringValue)
                return Guid.TryParse(stringValue, out _);

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} must be a valid GUID.";
        }
    }

    /// <summary>
    /// Validates that a date is not in the past
    /// </summary>
    public class FutureDateAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null)
                return true; // Allow null, use [Required] for mandatory fields

            if (value is DateTime dateTime)
                return dateTime > DateTime.Now;

            if (value is DateOnly dateOnly)
                return dateOnly > DateOnly.FromDateTime(DateTime.Now);

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name} must be a future date.";
        }
    }
}
