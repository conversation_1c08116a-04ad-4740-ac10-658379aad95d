﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class ApiResponseDto<T>
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public required string Message { get; set; }

        [JsonProperty("status")]
        public int Status { get; set; }

        [JsonProperty("data")]
        public T? Data { get; set; }

        [JsonProperty("errors")]
        public List<Error>? Errors { get; set; }

        public static ApiResponseDto<T> SuccessResponse(string message, int status)
        {
            return new ApiResponseDto<T> { Success = true, Message = message, Status = status };
        }
        public static ApiResponseDto<T> SuccessResponse(string message, int status, T data)
        {
            return new ApiResponseDto<T> { Success = true, Message = message, Data = data, Status = status };
        }

        public static ApiResponseDto<T> ErrorResponse(string message, int status, List<Error>? errors = null)
        {
            return new ApiResponseDto<T> { Success = false, Message = message, Status = status, Errors = errors };
        }

        public static ApiResponseDto<T> ErrorResponse(string message, int status, T data, List<Error>? errors = null)
        {
            return new ApiResponseDto<T> { Success = false, Message = message, Status = status, Data = data, Errors = errors };
        }
    }
    public class Error
    {
        [JsonProperty("message")]
        public required string Message { get; set; }

        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("field")]
        public string? Field { get; set; }
    }
}
