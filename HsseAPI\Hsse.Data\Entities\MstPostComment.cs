﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstPostComment
{
    public int CommentId { get; set; }

    public int PostId { get; set; }

    public int UserId { get; set; }

    public string? CommentText { get; set; }

    public DateTime? CommentedAt { get; set; }

    public int? ParentCommentId { get; set; }

    public virtual ICollection<MstCommentLike> MstCommentLikes { get; set; } = new List<MstCommentLike>();

    public virtual MstPost Post { get; set; } = null!;

    public virtual MstUser User { get; set; } = null!;
}
