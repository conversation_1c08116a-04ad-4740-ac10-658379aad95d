using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class EventRepository : IEventRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly IStorageRepository _storageRepository;
        public EventRepository(MasterDBContext masterDBContext, IStorageRepository storageRepository)
        {
            _MasterDBContext = masterDBContext;
            _storageRepository = storageRepository;

        }

        public long CreateOrUpdateEvent(CreateEventDto createEventDto)
        {
            if (!string.IsNullOrEmpty(createEventDto.ImageBase64))
            {
                createEventDto.FileName = Guid.NewGuid().ToString() + ".jpeg";
                _storageRepository.UploadBlob(createEventDto.ImageBase64, createEventDto.FileName, "event-documents");
            }
            if (createEventDto.EventID == 0)
            {
                // Create new event
                var newEvent = new MstEvent
                {
                    Title = createEventDto.Title,
                    Description = createEventDto.Description,
                    MediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/event-documents/" + createEventDto.FileName,
                    EventDateTime = createEventDto.EventDateTime,
                    Location = createEventDto.Location,
                    ExternalLink = createEventDto.ExternalLink,
                    CreatedBy = createEventDto.CreatedBy,
                    CreatedAt = DateTime.Now,
                    FacilityId = createEventDto.FacilityID,
                    IsActive = createEventDto.IsActive,
                    ScheduleAt = createEventDto.ScheduleAt,
                    ExpiryAt = createEventDto.ExpiryAt,
                    IsRsvp = createEventDto.IsRsvp
                };

                _MasterDBContext.MstEvents.Add(newEvent);
                _MasterDBContext.SaveChanges();

                // Add receivers
                foreach (var userId in createEventDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = newEvent.EventId,
                        UserId = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createEventDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = newEvent.EventId,
                        GroupId = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                _MasterDBContext.SaveChanges();
                return newEvent.EventId;
            }
            else
            {
                // Update existing event
                var existingEvent = _MasterDBContext.MstEvents
                    .FirstOrDefault(e => e.EventId == createEventDto.EventID);

                if (existingEvent == null)
                    return 0;

                existingEvent.Title = createEventDto.Title;
                existingEvent.Description = createEventDto.Description;
                existingEvent.MediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/event-documents/" + createEventDto.FileName;
                existingEvent.EventDateTime = createEventDto.EventDateTime;
                existingEvent.Location = createEventDto.Location;
                existingEvent.ExternalLink = createEventDto.ExternalLink;
                existingEvent.FacilityId = createEventDto.FacilityID;
                existingEvent.IsActive = createEventDto.IsActive;
                existingEvent.ScheduleAt = createEventDto.ScheduleAt;
                existingEvent.ExpiryAt = createEventDto.ExpiryAt;
                existingEvent.IsRsvp = createEventDto.IsRsvp;

                // Remove existing receivers, then add new ones
                var existingReceivers = _MasterDBContext.MstAnnouncementReceivers
                    .Where(r => r.EventId == createEventDto.EventID);
                _MasterDBContext.MstAnnouncementReceivers.RemoveRange(existingReceivers);

                // Add new receivers
                foreach (var userId in createEventDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = createEventDto.EventID,
                        UserId = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createEventDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = createEventDto.EventID,
                        GroupId = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                _MasterDBContext.SaveChanges();
                return existingEvent.EventId;
            }
        }

        public List<GetEventResponseDto> GetEvents(int userId)
        {
            var eventIds = _MasterDBContext.MstAnnouncementReceivers.Where(x => x.UserId == userId && x.EventId != null).Select(x => x.EventId).ToList();
            if (eventIds == null || eventIds.Count == 0)
            {
                return null;
            }
            var events = _MasterDBContext.MstEvents.Where(x => eventIds.Contains(x.EventId)).ToList();
            var eventDtos = new List<GetEventResponseDto>();
            foreach (var ev in events)
            {
                var eventDto = new GetEventResponseDto
                {
                    EventId = ev.EventId,
                    Title = ev.Title,
                    Description = ev.Description,
                    MediaUrl = ev.MediaUrl,
                    EventDateTime = ev.EventDateTime,
                    Location = ev.Location,
                    ExternalLink = ev.ExternalLink,
                    CreatedAt = ev.CreatedAt,
                    CreatedBy = ev.CreatedBy,
                    FacilityId = ev.FacilityId,
                    IsActive = ev.IsActive,
                    ScheduleAt = ev.ScheduleAt,
                    ExpiryAt = ev.ExpiryAt,
                    IsRsvp = ev.IsRsvp
                };
                // Get event responses
                var responses = _MasterDBContext.MstEventResponses
                    .Where(r => r.EventId == ev.EventId)
                    .Select(r => new EventResponseResponseDto
                    {
                        ResponseId = r.ResponseId,
                        EventId = r.EventId,
                        UserId = r.UserId,
                        IsAccepted = r.IsAccepted,
                        RespondedAt = r.RespondedAt
                    }).ToList();
                eventDto.eventResponseResponseDto = responses.FirstOrDefault(); // Assuming we want the first response
                eventDtos.Add(eventDto);
            }
            return eventDtos;

        }
        public int CreateOrUpdateEventResponse(EventResponseDto dto)
        {
            var eventResponseExist = _MasterDBContext.MstEventResponses
                .FirstOrDefault(r => r.EventId == dto.EventId && r.UserId == dto.UserId);
            if (eventResponseExist == null) {
                // No existing response, create a new one
                var newResponse = new MstEventResponse
                {
                    EventId = dto.EventId,
                    UserId = dto.UserId,
                    IsAccepted = dto.IsAccepted,
                    RespondedAt = DateTime.UtcNow
                };
                _MasterDBContext.MstEventResponses.Add(newResponse);
                _MasterDBContext.SaveChanges();
                return 1;
            }
            else
            {
                // Update existing response
                eventResponseExist.IsAccepted = dto.IsAccepted;
                eventResponseExist.RespondedAt = DateTime.UtcNow;
                _MasterDBContext.SaveChanges();
                return 2;
            }


        }
    }
}
