﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstFacility
{
    public int FacilityId { get; set; }

    public int OrgId { get; set; }

    public string? FacilityName { get; set; }

    public string? FacilityCode { get; set; }

    public DateTime? CreatedAt { get; set; }

    public bool IsActive { get; set; }

    public virtual ICollection<MstActionParty> MstActionParties { get; set; } = new List<MstActionParty>();

    public virtual ICollection<MstAnnouncement> MstAnnouncements { get; set; } = new List<MstAnnouncement>();

    public virtual ICollection<MstDeviceDetail> MstDeviceDetails { get; set; } = new List<MstDeviceDetail>();

    public virtual ICollection<MstEvent> MstEvents { get; set; } = new List<MstEvent>();

    public virtual ICollection<MstFeedback> MstFeedbacks { get; set; } = new List<MstFeedback>();

    public virtual ICollection<MstInspection> MstInspections { get; set; } = new List<MstInspection>();

    public virtual ICollection<MstNewsletter> MstNewsletters { get; set; } = new List<MstNewsletter>();

    public virtual ICollection<MstPost> MstPosts { get; set; } = new List<MstPost>();

    public virtual ICollection<MstUserFacilityMapping> MstUserFacilityMappings { get; set; } = new List<MstUserFacilityMapping>();

    public virtual ICollection<MstUserRolesConfig> MstUserRolesConfigs { get; set; } = new List<MstUserRolesConfig>();

    public virtual MstOrganisation Org { get; set; } = null!;
}
