﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.Enum;
using Hsse.Infrastructure.IRepositories;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly IStorageRepository _storageRepository;
        public PostRepository(MasterDBContext masterDBContext, IStorageRepository storageRepository)
        {
            _MasterDBContext = masterDBContext;
            _storageRepository = storageRepository;
        }
        public UserProfile GetUsersByUserId(int userId)
        {
            try
            {
                var userProfile = new UserProfile();

                var userExist = _MasterDBContext.MstUsers.Where(x => x.UserId == userId).FirstOrDefault();
                if (userExist != null)
                {
                    var userResponse = new UserResponseDto
                    {
                        UserId = userExist.UserId,
                        Username = userExist.Username,
                        FirstName = userExist.FirstName,
                        LastName = userExist.LastName,
                        Email = userExist.Email,
                        ProfileImageUrl = userExist.ProfileImageUrl,
                        Bio = userExist.Bio,
                        PrimaryFacilityId = userExist.PrimaryFacilityId,
                        CreatedAt = userExist.CreatedAt,
                        CreatedBy = userExist.CreatedBy,
                        LastLogin = userExist.LastLogin,
                        IsActive = userExist.IsActive,
                        SsoUserId = userExist.SsoUserId,
                        EmployeeCode = userExist.EmployeeCode,
                        ContactNumber = userExist.ContactNumber,
                        LanguageId = userExist.LanguageId

                    };
                    var userPosts = _MasterDBContext.MstPosts.Where(p => !p.IsDeleted && (p.UserId == userId || p.ClosedBy == userId)).ToList();

                    var countsDto = new PostsCountsDto
                    {
                        NewPosts = userPosts.Count(p => p.UserId == userId),
                        Closure = userPosts.Count(p => p.ClosedBy == userId),
                        Points = 500
                    };

                    userProfile.User = userResponse;
                    userProfile.postsCountsDto = countsDto;
                    return userProfile;
                }
                return null;

            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
        public long CreatePost(CreatePostDto createPostDto)
        {
            if (!string.IsNullOrEmpty(createPostDto.ImageBase64))
            {
                createPostDto.FileName = Guid.NewGuid().ToString() + ".jpeg";
                _storageRepository.UploadBlob(createPostDto.ImageBase64, createPostDto.FileName, "post-media");
            }
            if (createPostDto.PostID == 0)
            {
                // Create new post
                var newPost = new MstPost
                {
                    UserId = createPostDto.UserID,
                    FacilityId = createPostDto.FacilityID,
                    Title = createPostDto.Title,
                    Description = createPostDto.Description,
                    PostType = createPostDto.PostType,
                    Location = createPostDto.Location,
                    TaggedCategoryId = createPostDto.TaggedCategoryId,
                    RequiresFollowup = createPostDto.RequiresFollowup,
                    Status = 0,
                    CreatedAt = DateTime.Now,
                    IsDeleted = false
                };

                _MasterDBContext.MstPosts.Add(newPost);
                _MasterDBContext.SaveChanges();

                if (!string.IsNullOrEmpty(createPostDto.FileName))
                {
                    var media = new MstPostMedium
                    {
                        PostId = newPost.PostId,
                        MediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/post-media/" + createPostDto.FileName,
                        CreatedAt = DateTime.Now
                    };

                    _MasterDBContext.MstPostMedia.Add(media);
                    _MasterDBContext.SaveChanges();
                }

                return 1;
            }
            else
            {
                // Update existing post
                var existingPost = _MasterDBContext.MstPosts
                    .FirstOrDefault(p => p.PostId == createPostDto.PostID && !p.IsDeleted);

                if (existingPost == null)
                    return 0;

                existingPost.UserId = createPostDto.UserID;
                existingPost.FacilityId = createPostDto.FacilityID;
                existingPost.Title = createPostDto.Title;
                existingPost.Description = createPostDto.Description;
                existingPost.PostType = createPostDto.PostType;
                existingPost.Location = createPostDto.Location;
                existingPost.TaggedCategoryId = createPostDto.TaggedCategoryId;
                existingPost.RequiresFollowup = createPostDto.RequiresFollowup;
                existingPost.Status = 0;
                existingPost.UpdatedAt = DateTime.Now;

                var existingMedia = _MasterDBContext.MstPostMedia
                    .FirstOrDefault(m => m.PostId == createPostDto.PostID);

                if (!string.IsNullOrEmpty(createPostDto.FileName))
                {
                    if (existingMedia != null)
                    {
                        existingMedia.MediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/post-media/" + createPostDto.FileName;
                    }
                    else
                    {
                        var newMedia = new MstPostMedium
                        {
                            PostId = createPostDto.PostID,
                            MediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/post-media/" + createPostDto.FileName,
                            CreatedAt = DateTime.Now
                        };
                        _MasterDBContext.MstPostMedia.Add(newMedia);
                    }
                }

                _MasterDBContext.SaveChanges();
                return 2;
            }
        }
        public (List<PostDetailsDto>, int) GetPosts(PaginationDto pagination, int? userId = null)
        {
            var postsQuery = _MasterDBContext.MstPosts
                .Where(p => !p.IsDeleted);

            if (userId.HasValue)
                postsQuery = postsQuery.Where(p => p.UserId == userId.Value);

            var posts = postsQuery
                .Select(post => new PostDetailsDto
                {
                    PostID = post.PostId,
                    UserID = post.UserId,
                    Title = post.Title,
                    Description = post.Description,
                    PostType = post.PostType,
                    Location = post.Location,
                    TaggedCategoryId = post.TaggedCategoryId,
                    RequiresFollowup = post.RequiresFollowup,
                    Status = ((PostStatus)post.Status).ToString(),
                    CreatedAt = post.CreatedAt,
                    CommentsCount = _MasterDBContext.MstPostComments.Count(c => c.PostId == post.PostId),
                    LikesCount = _MasterDBContext.MstLikesConfigs.Count(l => l.PostId == post.PostId && l.IsLiked),
                    MediaUrl = _MasterDBContext.MstPostMedia
                        .Where(m => m.PostId == post.PostId)
                        .Select(m => m.MediaUrl)
                        .FirstOrDefault(),
                    AfterMediaUrl = _MasterDBContext.MstPostMedia
                        .Where(m => m.PostId == post.PostId)
                        .Select(m => m.AfterMediaUrl)
                        .FirstOrDefault(),
                })
                .OrderByDescending(p => p.CreatedAt)
                .ToList();
            var totalRecords = posts.Count();

            var pagedData = posts
                                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                                .Take(pagination.PageSize)
                                .ToList();

            return (pagedData, totalRecords);
        }

        public List<MstPostCategory> GetPostCategories()
        {
            return _MasterDBContext.MstPostCategories.ToList();
        }
        public int CreateOrUpdateLikes(CreateLikeDto createLikeDto)
        {
            // Ensure exactly one of PostID or EventID is provided
            if ((createLikeDto.PostID == null && createLikeDto.EventID == null) ||
                (createLikeDto.PostID != null && createLikeDto.EventID != null))
            {
                throw new ArgumentException("Like must be for either a Post or an Event, not both.");
            }

            // Query only for one of the non-null keys
            var result = _MasterDBContext.MstLikesConfigs.FirstOrDefault(x =>
                x.UserId == createLikeDto.UserID &&
                ((createLikeDto.PostID != null && x.PostId == createLikeDto.PostID) ||
                 (createLikeDto.EventID != null && x.EventId == createLikeDto.EventID)));

            if (result == null)
            {
                var newLike = new MstLikesConfig
                {
                    UserId = createLikeDto.UserID,
                    PostId = createLikeDto.PostID,
                    EventId = createLikeDto.EventID,
                    IsLiked = createLikeDto.IsLiked,
                    LikedAt = DateTime.Now
                };

                _MasterDBContext.MstLikesConfigs.Add(newLike);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }

            // Update existing
            result.IsLiked = createLikeDto.IsLiked;
            result.LikedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 2; // Updated
        }

        public int ClosePost(ClosedPostDto closedPostDto)
        {
            if (!string.IsNullOrEmpty(closedPostDto.ImageBase64))
            {
                closedPostDto.FileName = Guid.NewGuid().ToString() + ".jpeg";
                _storageRepository.UploadBlob(closedPostDto.ImageBase64, closedPostDto.FileName, "post-media");
            }
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostId == closedPostDto.PostID && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.ClosedBy = closedPostDto.UserID;
            post.ClosedDescription = closedPostDto.ClosedDescription;
            post.UpdatedAt = DateTime.Now;
            // Assuming status 3 means closed - adjust based on your business logic
            post.Status = 3;
            _MasterDBContext.SaveChanges();

            var existingMedia = _MasterDBContext.MstPostMedia
                   .FirstOrDefault(m => m.PostId == closedPostDto.PostID);

            if (!string.IsNullOrEmpty(closedPostDto.FileName))
            {
                if (existingMedia != null)
                {
                    existingMedia.AfterMediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/post-media/" + closedPostDto.FileName;
                }
                else
                {
                    var newMedia = new MstPostMedium
                    {
                        PostId = closedPostDto.PostID,
                        AfterMediaUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/post-media/" + closedPostDto.FileName,
                        CreatedAt = DateTime.Now
                    };
                    _MasterDBContext.MstPostMedia.Add(newMedia);
                }
            }
            // Follow-up Post
            var followupPost = _MasterDBContext.MstFollowupPosts.FirstOrDefault(f => f.PostId == closedPostDto.PostID);
            if (followupPost == null)
            {
                var followup = new MstFollowupPost
                {
                    PostId = closedPostDto.PostID,
                    CompletedBy = closedPostDto.UserID,
                    CompletedTime = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = closedPostDto.UserID
                };
                _MasterDBContext.MstFollowupPosts.Add(followup);
            }
            else
            {
                followupPost.CompletedBy = closedPostDto.UserID;
                followupPost.CompletedTime = DateTime.UtcNow;
            }

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int DeletePost(int postId, int deletedBy)
        {
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostId == postId && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.DeletedBy = deletedBy;
            post.IsDeleted = true;
            post.UpdatedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int CreateOrUpdateComment(CreateCommentDto createCommentDto)
        {
            if (createCommentDto.CommentID == 0)
            {
                // Create new comment
                var newComment = new MstPostComment
                {
                    PostId = createCommentDto.PostID,
                    UserId = createCommentDto.UserID,
                    CommentText = createCommentDto.CommentText,
                    CommentedAt = DateTime.Now
                };
                if (createCommentDto.ParentCommentID > 0)
                {
                    newComment.ParentCommentId = createCommentDto.ParentCommentID;
                }

                _MasterDBContext.MstPostComments.Add(newComment);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing comment
                var existingComment = _MasterDBContext.MstPostComments
                    .FirstOrDefault(c => c.CommentId == createCommentDto.CommentID);

                if (existingComment == null)
                    return 0; // Comment not found

                existingComment.CommentText = createCommentDto.CommentText;
                existingComment.CommentedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }
        public int CreateOrUpdateCommentLikes(CommentLikeCreateDto commentLikeDto)
        {
            // Ensure CommentID is provided
            if (commentLikeDto.CommentId == 0)
            {
                throw new ArgumentException("CommentID must be provided for liking a comment.");
            }
            // Check if the like already exists
            var existingLike = _MasterDBContext.MstCommentLikes
                .FirstOrDefault(x => x.UserId == commentLikeDto.UserId && x.CommentId == commentLikeDto.CommentId);
            if (existingLike == null)
            {
                // Create new like
                var newLike = new MstCommentLike
                {
                    UserId = commentLikeDto.UserId,
                    CommentId = commentLikeDto.CommentId,
                    IsLiked = commentLikeDto.IsLiked,
                    CreatedAt = DateTime.Now
                };
                _MasterDBContext.MstCommentLikes.Add(newLike);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing like
                existingLike.IsLiked = commentLikeDto.IsLiked;
                existingLike.CreatedAt = DateTime.Now;
                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }
        public List<CommentResponseDto> GetCommentsByPostId(int PostId)
        {
            var result = _MasterDBContext.MstPostComments
                        .Where(c => c.PostId == PostId).ToList();
            var commentTree = BuildCommentTree(result);

            return commentTree;
        }
        private List<CommentResponseDto> BuildCommentTree(List<MstPostComment> allComments, int? parentId = null)
        {
            return allComments
                .Where(c => c.ParentCommentId == parentId)
                .Select(c => new CommentResponseDto
                {
                    CommentID = c.CommentId,
                    ParentCommentID = c.ParentCommentId,
                    UserID = c.UserId,
                    Name = _MasterDBContext.MstUsers
                                .Where(u => u.UserId == c.UserId)
                                .Select(u => u.FirstName + " " + u.LastName)
                                .FirstOrDefault(),
                    Profile = _MasterDBContext.MstUsers
                                .Where(u => u.UserId == c.UserId)
                                .Select(u => u.ProfileImageUrl)
                                .FirstOrDefault(),
                    CommentText = c.CommentText,
                    CommentedAt = c.CommentedAt,
                    LikesCount = _MasterDBContext.MstCommentLikes.Count(l => l.CommentId == c.CommentId && l.IsLiked),
                    Likes = _MasterDBContext.MstCommentLikes
                                .Where(l => l.CommentId == c.CommentId)
                                .Select(l => new CommentLikeDto
                                {
                                    CommentLikeId = l.CommentLikeId,
                                    CommentId = l.CommentId,
                                    Isliked = l.IsLiked,
                                    UserId = l.UserId,
                                    Name = _MasterDBContext.MstUsers
                                        .Where(u => u.UserId == l.UserId)
                                        .Select(u => u.FirstName + " " + u.LastName)
                                        .FirstOrDefault(),
                                    Profile = _MasterDBContext.MstUsers
                                        .Where(u => u.UserId == l.UserId) // ✅ Corrected to l.UserId instead of c.UserId
                                        .Select(u => u.ProfileImageUrl)
                                        .FirstOrDefault(),
                                }).ToList(),
                    Replies = BuildCommentTree(allComments, c.CommentId) // ✅ Correct recursion parameter
                }).ToList();
        }

        public List<LikeReposeDto> GetLikesByPostId(int PostId)
        {
            var result = _MasterDBContext.MstLikesConfigs
                        .Where(l => l.PostId == PostId)
                        .Select(l => new LikeReposeDto
                        {
                            LikeID = l.LikeId,
                            UserID = l.UserId,
                            Name = _MasterDBContext.MstUsers
                                .Where(u => u.UserId == l.UserId)
                                .Select(u => u.FirstName + " " + u.LastName)
                                .FirstOrDefault(),
                            Profile = _MasterDBContext.MstUsers.Where(u => u.UserId == l.UserId)
                                .Select(u => u.ProfileImageUrl)
                                .FirstOrDefault(),
                            IsLiked = l.IsLiked,
                            LikedAt = l.LikedAt
                        }).ToList();
            return result;
        }
        public UserResponseDto UpdateUser(UpdateUserRequestDto dto)
        {
            try
            {
                if (!string.IsNullOrEmpty(dto.ImageBase64))
                {
                    dto.FileName = Guid.NewGuid().ToString() + ".jpeg";
                    _storageRepository.UploadBlob(dto.ImageBase64, dto.FileName, "user-profile");
                }
                var userExist = _MasterDBContext.MstUsers.Where(x => x.UserId == dto.UserId).FirstOrDefault();
                if (userExist != null)
                {
                    userExist.FirstName = dto.FirstName;
                    userExist.LastName = dto.LastName;
                    userExist.Email = dto.Email;
                    userExist.ProfileImageUrl = "https://uetrackstorage.blob.core.windows.net/hssev2/user-profile/" + dto.FileName;
                    userExist.Bio = dto.Bio;
                    userExist.ContactNumber = dto.ContactNumber;
                    _MasterDBContext.MstUsers.Attach(userExist);
                    _MasterDBContext.SaveChanges();

                    var result = new UserResponseDto
                    {
                        UserId = userExist.UserId,
                        Username = userExist.Username,
                        FirstName = userExist.FirstName,
                        LastName = userExist.LastName,
                        Email = userExist.Email,
                        ProfileImageUrl = userExist.ProfileImageUrl,
                        Bio = dto.Bio,
                        PrimaryFacilityId = userExist?.PrimaryFacilityId,
                        CreatedAt = userExist.CreatedAt,
                        CreatedBy = userExist.CreatedBy,
                        LastLogin = userExist.LastLogin,
                        IsActive = userExist.IsActive,
                        SsoUserId = userExist.UserId,
                        EmployeeCode = userExist.EmployeeCode,
                        ContactNumber = userExist.ContactNumber,
                        LanguageId = userExist.LanguageId
                    };
                    return result;
                }
                return null;
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
        public int AssignPost(AssignPostDto dto)
        {
            if (dto.PostId <= 0)
                return 0;

            var post = _MasterDBContext.MstPosts.FirstOrDefault(p => p.PostId == dto.PostId && !p.IsDeleted);
            if (post == null)
                return 0; // Post not found

            // Update Post
            post.Status = 1;
            post.UpdatedAt = DateTime.Now;

            // Follow-up Post
            var followupPost = _MasterDBContext.MstFollowupPosts.FirstOrDefault(f => f.PostId == dto.PostId);
            if (followupPost == null)
            {
                var followup = new MstFollowupPost
                {
                    PostId = dto.PostId,
                    AssignedTo = dto.AssignTo.ToString(),
                    AssignedTime = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = dto.UserId
                };
                _MasterDBContext.MstFollowupPosts.Add(followup);
            }
            else
            {
                followupPost.AssignedTo = dto.AssignTo.ToString();
                followupPost.AssignedTime = DateTime.UtcNow;
            }

            // Save all changes at once
            _MasterDBContext.SaveChanges();
            return 1;

        }
        public int FollowupPost(FollowupDto dto)
        {
            if (dto.PostId <= 0)
                return 0;

            var post = _MasterDBContext.MstPosts.FirstOrDefault(p => p.PostId == dto.PostId && !p.IsDeleted);
            if (post == null)
                return 0; // Post not found

            // Update Post
            post.Status = 2;
            post.UpdatedAt = DateTime.Now;

            // Follow-up Post
            var followupPost = _MasterDBContext.MstFollowupPosts.FirstOrDefault(f => f.PostId == dto.PostId);
            if (followupPost == null)
            {
                var followup = new MstFollowupPost
                {
                    PostId = dto.PostId,
                    FollowedupBy = dto.Followup,
                    FollowedupTime = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = dto.UserId
                };
                _MasterDBContext.MstFollowupPosts.Add(followup);
            }
            else
            {
                followupPost.FollowedupBy = dto.Followup;
                followupPost.FollowedupTime = DateTime.UtcNow;
            }

            // Save all changes at once
            _MasterDBContext.SaveChanges();
            return 1;

        }
        public int ArchivedPost(ArchivedDto dto)
        {
            if (dto.PostId <= 0)
                return 0;

            var post = _MasterDBContext.MstPosts.FirstOrDefault(p => p.PostId == dto.PostId && !p.IsDeleted);
            if (post == null)
                return 0; // Post not found

            // Update Post
            post.Status = 4;
            post.UpdatedAt = DateTime.Now;

            // Follow-up Post
            var followupPost = _MasterDBContext.MstFollowupPosts.FirstOrDefault(f => f.PostId == dto.PostId);
            if (followupPost == null)
            {
                var followup = new MstFollowupPost
                {
                    PostId = dto.PostId,
                    ArchivedBy = dto.Archived,
                    ArchivedTime = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = dto.UserId
                };
                _MasterDBContext.MstFollowupPosts.Add(followup);
            }
            else
            {
                followupPost.ArchivedBy = dto.Archived;
                followupPost.ArchivedTime = DateTime.UtcNow;
            }

            // Save all changes at once
            _MasterDBContext.SaveChanges();
            return 1;
        }
        public (List<PostDetailsDto>, int) GetAssignedPosts(PaginationDto pagination, int userId)
        {
            var postsQuery = _MasterDBContext.MstPosts
                .Where(p => !p.IsDeleted && p.Status == (int)PostStatus.Assigned)
                .Where(p => _MasterDBContext.MstFollowupPosts.Any(f => f.PostId == p.PostId && f.AssignedTo.Contains(userId.ToString())));
            var posts = postsQuery
                .Select(post => new PostDetailsDto
                {
                    PostID = post.PostId,
                    UserID = post.UserId,
                    Title = post.Title,
                    Description = post.Description,
                    PostType = post.PostType,
                    Location = post.Location,
                    TaggedCategoryId = post.TaggedCategoryId,
                    RequiresFollowup = post.RequiresFollowup,
                    Status = ((PostStatus)post.Status).ToString(),
                    CreatedAt = post.CreatedAt,
                    CommentsCount = _MasterDBContext.MstPostComments.Count(c => c.PostId == post.PostId),
                    LikesCount = _MasterDBContext.MstLikesConfigs.Count(l => l.PostId == post.PostId && l.IsLiked),
                    MediaUrl = _MasterDBContext.MstPostMedia
                        .Where(m => m.PostId == post.PostId)
                        .Select(m => m.MediaUrl)
                        .FirstOrDefault(),
                    AfterMediaUrl = _MasterDBContext.MstPostMedia
                        .Where(m => m.PostId == post.PostId)
                        .Select(m => m.AfterMediaUrl)
                        .FirstOrDefault(),
                })
                .OrderByDescending(p => p.CreatedAt)
                .ToList();
            var totalRecords = posts.Count();
            var pagedData = posts
                                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                                .Take(pagination.PageSize)
                                .ToList();
            return (pagedData, totalRecords);
        }
    }
}
