using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class CreateInspectionDto
    {
        public int InspectionId { get; set; }
        public int? FacilityID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime InspectionDate { get; set; }
        public int? CreatedBy { get; set; }
        public string ReferenceNo { get; set; }
        public List<CreateInspectionItemDto> InspectionItems { get; set; } = new List<CreateInspectionItemDto>();
    }

    public class CreateInspectionItemDto
    {
        public int ItemId { get; set; }
        public string Description { get; set; }
        public string SpecificLocation { get; set; }
        public string Recommendation { get; set; }
        public string? ActionPartyName { get; set; }
        public int? Status { get; set; }
        public string Rectification { get; set; }
        public string AfterImagePath { get; set; }
        public DateTime? CompletionDateTime { get; set; }
        public string RecommendationMediaUrl { get; set; }
        public string Observation { get; set; }
        public string ObservationMediaUrl { get; set; }
        public int? Verification { get; set; }
    }

    public class VerifyActionPartyDto
    {
        public int ItemId { get; set; }
        public int ActionPartyId { get; set; }
        public int VerifiedBy { get; set; }
        public string? Description { get; set; }
        public string? AfterImageBase64 { get; set; }
        public string? AfterImageFileName { get; set; }
    }

    public class VerifyInspectorDto
    {
        public int ItemId { get; set; }
        public int InspectionId { get; set; }
        public int InspectorId { get; set; }
        public bool IsVerified { get; set; }
        public string? Description { get; set; }
        public string? RecomendationImageBase64 { get; set; }
        public string? RecomendationImageFileName { get; set; }
    }

    public class MstInspectionDto
    {
        public int InspectionId { get; set; }
        public int? FacilityId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public DateTime? InspectionDate { get; set; }
        public string? ReferenceNo { get; set; }
        public int? CreatedBy { get; set; }
        public int? TypeOfInspection { get; set; }
        public string? SpecificLocation { get; set; }
        public List<InspectionItemsDto> InspectionItems { get; set; } = new List<InspectionItemsDto>();

    }
    public class InspectionItemsDto
    {
        public int? ItemId { get; set; }
        public string? SpecificLocation { get; set; }
        public string? Description { get; set; }
        public int? ActionPartyId { get; set; }
        public string? Observation { get; set; }
        public string? ObservationAttachmentBase64 { get; set; }
        public string? ObservationAttachmentName { get; set; }
        public string? Recommendation { get; set; }
        public string? RecommendationAttachmentBase64 { get; set; }
        public string? RecommendationAttachmentName { get; set; }
        public int? ContactPersonId { get; set; }
    }
}
