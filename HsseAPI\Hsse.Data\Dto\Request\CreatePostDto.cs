﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Data.Attributes;

namespace Hsse.Data.Dto.Request
{
    public class CreatePostDto
    {
        public int PostID { get; set; }

        [Required(ErrorMessage = "User ID is required")]
        [PositiveNumber(ErrorMessage = "User ID must be a positive number")]
        public int UserID { get; set; }

        [PositiveNumber(ErrorMessage = "Facility ID must be a positive number")]
        public int? FacilityID { get; set; }

        [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
        public string? Title { get; set; }

        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
        public string? Description { get; set; }

        [StringLength(50, ErrorMessage = "Post type cannot exceed 50 characters")]
        public string? PostType { get; set; }

        [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
        public string? Location { get; set; }

        [PositiveNumber(ErrorMessage = "Tagged Category ID must be a positive number")]
        public int? TaggedCategoryId { get; set; }

        [StringLength(10, ErrorMessage = "Requires followup cannot exceed 10 characters")]
        public string? RequiresFollowup { get; set; }

        [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
        public string? Status { get; set; }

        [Base64Validation(ErrorMessage = "Image must be a valid Base64 string")]
        public string? ImageBase64 { get; set; }

        [StringLength(255, ErrorMessage = "File name cannot exceed 255 characters")]
        public string? FileName { get; set; }
    }
}
