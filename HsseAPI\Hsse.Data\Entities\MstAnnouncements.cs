﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstAnnouncements
    {
        [Key]
        public int Announcements_id { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public int? Status { get; set; }
        public DateTime? Created_at { get; set; }
        public int? Created_by { get; set; }
        public DateTime? Modified_at { get; set; }
        public int? Modified_by { get; set; }
        public string? AnnouncementDocument { get; set; }
        public DateTime? ScheduleAt { get; set; }
        public DateTime? ExpiryAt { get; set; }
        public int? FacilityID { get; set; }
        public int? CategoryId { get; set; }
    }
}
