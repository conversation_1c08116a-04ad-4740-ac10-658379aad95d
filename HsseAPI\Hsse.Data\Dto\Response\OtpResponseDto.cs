﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class OtpTriggerResponseDto
    {
        public int Status { get; set; }
        public string? Message { get; set; }
        public Guid? ReferenceId { get; set; }
        public DateTime? ExpiryTime { get; set; }
        public int? ResendAttempts { get; set; }
    }

    public class OtpValidationResponseDto
    {
        public int Status { get; set; }
        public string? Message { get; set; }
        public bool IsValid { get; set; }
        public DateTime? ValidatedAt { get; set; }
        public int? RemainingAttempts { get; set; }
    }

    public class OtpResendResponseDto
    {
        public int Status { get; set; }
        public string? Message { get; set; }
        public Guid? ReferenceId { get; set; }
        public DateTime? ExpiryTime { get; set; }
        public int? ResendAttempts { get; set; }
        public int? MaxResendAttempts { get; set; }
    }
}
