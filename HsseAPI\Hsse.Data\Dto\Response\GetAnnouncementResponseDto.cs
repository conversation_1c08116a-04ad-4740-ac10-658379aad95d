﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class GetAnnouncementResponseDto
    {
        public int AnnouncementsId { get; set; }

        public string Title { get; set; } = null!;

        public string Description { get; set; } = null!;

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? ModifiedBy { get; set; }

        public string? AnnouncementDocument { get; set; }

        public DateTime? ScheduleAt { get; set; }

        public DateTime? ExpiryAt { get; set; }

        public int? FacilityId { get; set; }

        public int? CategoryId { get; set; }

        public string? Attachment { get; set; }
    }
}
