﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Hsse.Data.Dto.Response;

namespace Hsse.API.Controllers.v1
{
    public class BaseAPIController : ControllerBase
    {
        /// <summary>
        /// Creates a standardized error response for validation failures
        /// </summary>
        /// <param name="message">Error message</param>
        /// <returns>BadRequest with ValidationErrorResponse</returns>
        protected IActionResult ValidationError(string message)
        {
            var response = new ValidationErrorResponse
            {
                Message = message
            };
            return BadRequest(response);
        }

        /// <summary>
        /// Creates a standardized success response
        /// </summary>
        /// <param name="data">Response data</param>
        /// <param name="message">Success message</param>
        /// <returns>Ok with ResponseDetails</returns>
        protected IActionResult SuccessResponse(object? data = null, string message = "Operation completed successfully")
        {
            var response = new ResponseDetails
            {
                Status = 1,
                Message = message,
                Result = data
            };
            return Ok(response);
        }

        /// <summary>
        /// Creates a standardized error response
        /// </summary>
        /// <param name="message">Error message</param>
        /// <returns>BadRequest with ResponseDetails</returns>
        protected IActionResult ErrorResponse(string message = "An error occurred")
        {
            var response = new ResponseDetails
            {
                Status = 0,
                Message = message,
                Result = null
            };
            return BadRequest(response);
        }
    }
}
