﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class AnnouncementRepository : IAnnouncementRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly IStorageRepository _storageRepository;
        public AnnouncementRepository(MasterDBContext masterDBContext, IStorageRepository storageRepository)
        {
            _MasterDBContext = masterDBContext;
            _storageRepository = storageRepository;
        }

        public long CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto)
        {
            if (!string.IsNullOrEmpty(createAnnouncementDto.ImageBase64))
            {
                createAnnouncementDto.Filename = Guid.NewGuid().ToString() + ".jpeg";
                _storageRepository.UploadBlob(createAnnouncementDto.ImageBase64, createAnnouncementDto.Filename, "announcement-documents");
            }
            if (createAnnouncementDto.AnnouncementsId == 0)
            {
                // Create new announcement  
                var newAnnouncement = new MstAnnouncement // Changed from MstAnnouncements to MstAnnouncement  
                {
                    Title = createAnnouncementDto.Title,
                    Description = createAnnouncementDto.Description,
                    Status = createAnnouncementDto.Status,
                    CreatedBy = createAnnouncementDto.CreatedBy,
                    CreatedAt = DateTime.Now,
                    AnnouncementDocument = "https://uetrackstorage.blob.core.windows.net/hssev2/announcement-documents/" + createAnnouncementDto.Filename,
                    ScheduleAt = createAnnouncementDto.ScheduleAt,
                    ExpiryAt = createAnnouncementDto.ExpiryAt,
                    FacilityId = createAnnouncementDto.FacilityID,
                    CategoryId = createAnnouncementDto.CategoryId
                };

                _MasterDBContext.MstAnnouncements.Add(newAnnouncement);
                _MasterDBContext.SaveChanges();

                // Add receivers  
                foreach (var userId in createAnnouncementDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceiver // Changed from MstAnnouncementReceivers to MstAnnouncementReceiver  
                    {
                        AnnouncementId = newAnnouncement.AnnouncementsId,
                        UserId = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createAnnouncementDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceiver // Changed from MstAnnouncementReceivers to MstAnnouncementReceiver  
                    {
                        AnnouncementId = newAnnouncement.AnnouncementsId,
                        GroupId = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                // Add documents  
                foreach (var doc in createAnnouncementDto.Documents)
                {
                    var document = new MstAnnouncementDocument // Changed from MstAnnouncementDocuments to MstAnnouncementDocument  
                    {
                        AnnouncementId = newAnnouncement.AnnouncementsId,
                        DocumentName = doc.DocumentName,
                        DocumentFile = doc.DocumentFile
                    };
                    _MasterDBContext.MstAnnouncementDocuments.Add(document);
                }

                _MasterDBContext.SaveChanges();
                return newAnnouncement.AnnouncementsId;
            }
            else
            {
                // Update existing announcement  
                var existingAnnouncement = _MasterDBContext.MstAnnouncements
                    .FirstOrDefault(a => a.AnnouncementsId == createAnnouncementDto.AnnouncementsId);

                if (existingAnnouncement == null)
                    return 0;

                existingAnnouncement.Title = createAnnouncementDto.Title;
                existingAnnouncement.Description = createAnnouncementDto.Description;
                existingAnnouncement.Status = createAnnouncementDto.Status;
                existingAnnouncement.ModifiedBy = createAnnouncementDto.ModifiedBy;
                existingAnnouncement.ModifiedAt = DateTime.Now;
                existingAnnouncement.AnnouncementDocument = "https://uetrackstorage.blob.core.windows.net/hssev2/announcement-documents/" + createAnnouncementDto.Filename;
                existingAnnouncement.ScheduleAt = createAnnouncementDto.ScheduleAt;
                existingAnnouncement.ExpiryAt = createAnnouncementDto.ExpiryAt;
                existingAnnouncement.FacilityId = createAnnouncementDto.FacilityID;
                existingAnnouncement.CategoryId = createAnnouncementDto.CategoryId;

                // Remove existing receivers and documents, then add new ones  
                var existingReceivers = _MasterDBContext.MstAnnouncementReceivers
                    .Where(r => r.AnnouncementId == createAnnouncementDto.AnnouncementsId);
                _MasterDBContext.MstAnnouncementReceivers.RemoveRange(existingReceivers);

                var existingDocuments = _MasterDBContext.MstAnnouncementDocuments
                    .Where(d => d.AnnouncementId == createAnnouncementDto.AnnouncementsId);
                _MasterDBContext.MstAnnouncementDocuments.RemoveRange(existingDocuments);

                // Add new receivers  
                foreach (var userId in createAnnouncementDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceiver // Changed from MstAnnouncementReceivers to MstAnnouncementReceiver  
                    {
                        AnnouncementId = createAnnouncementDto.AnnouncementsId,
                        UserId = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createAnnouncementDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceiver // Changed from MstAnnouncementReceivers to MstAnnouncementReceiver  
                    {
                        AnnouncementId = createAnnouncementDto.AnnouncementsId,
                        GroupId = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                // Add new documents  
                foreach (var doc in createAnnouncementDto.Documents)
                {
                    var document = new MstAnnouncementDocument // Changed from MstAnnouncementDocuments to MstAnnouncementDocument  
                    {
                        AnnouncementId = createAnnouncementDto.AnnouncementsId,
                        DocumentName = doc.DocumentName,
                        DocumentFile = doc.DocumentFile
                    };
                    _MasterDBContext.MstAnnouncementDocuments.Add(document);
                }

                _MasterDBContext.SaveChanges();
                return existingAnnouncement.AnnouncementsId;
            }
        }

        public int CreateOrUpdateCategory(CreateAnnouncementCategoryDto createCategoryDto)
        {
            if (createCategoryDto.AnnoucementCategoryId == 0)
            {
                // Create new category
                var newCategory = new MstAnnouncementCategory
                {
                    AnnoucementCategoryName = createCategoryDto.AnnoucementCategoryName,
                    Status = createCategoryDto.Status,
                    CreatedBy = createCategoryDto.CreatedBy,
                    CreatedAt = DateTime.Now
                };

                _MasterDBContext.MstAnnouncementCategories.Add(newCategory);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing category
                var existingCategory = _MasterDBContext.MstAnnouncementCategories
                    .FirstOrDefault(c => c.AnnoucementCategoryId == createCategoryDto.AnnoucementCategoryId);

                if (existingCategory == null)
                    return 0; // Category not found

                existingCategory.AnnoucementCategoryName = createCategoryDto.AnnoucementCategoryName;
                existingCategory.Status = createCategoryDto.Status;
                existingCategory.ModifiedBy = createCategoryDto.ModifiedBy;
                existingCategory.ModifiedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }

        public List<MstAnnouncementCategory> GetCategories()
        {
            return _MasterDBContext.MstAnnouncementCategories.ToList();
        }

        public List<GetAnnouncementResponseDto> GetAnnouncements(int userId)
        {
           var announcementIds = _MasterDBContext.MstAnnouncementReceivers.Where(x => x.UserId == userId && x.AnnouncementId != null).Select(x => x.AnnouncementId).ToList();
            if (announcementIds.Count == 0)
            {
                return null;
            }
            var announcements = _MasterDBContext.MstAnnouncements.Where(a => announcementIds.Contains(a.AnnouncementsId) && a.Status == 1)
                .Select(a => new GetAnnouncementResponseDto
                {
                    AnnouncementsId = a.AnnouncementsId,
                    Title = a.Title,
                    Description = a.Description,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    CreatedBy = a.CreatedBy,
                    ModifiedAt = a.ModifiedAt,
                    ModifiedBy = a.ModifiedBy,
                    AnnouncementDocument = a.AnnouncementDocument,
                    ScheduleAt = a.ScheduleAt,
                    ExpiryAt = a.ExpiryAt,
                    FacilityId = a.FacilityId,
                    CategoryId = a.CategoryId,
                    Attachment = a.Attachment
                }).ToList();
            return announcements;
        }
    }
}
