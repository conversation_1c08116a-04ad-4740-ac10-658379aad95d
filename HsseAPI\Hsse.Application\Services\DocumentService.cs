using Hsse.Application.IServices;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class DocumentService : IDocumentService
    {
        private readonly IDocumentRepository _IDocumentRepository;

        public DocumentService(IDocumentRepository documentRepository)
        {
            _IDocumentRepository = documentRepository;
        }

        public List<MstDocumentLibrary> GetAllDocuments()
        {
            var result = _IDocumentRepository.GetAllDocuments();
            return result;
        }

        public List<MstDocumentLibrary> GetDocumentsByFacility(int facilityId)
        {
            var result = _IDocumentRepository.GetDocumentsByFacility(facilityId);
            return result;
        }

        public List<string> GetDocumentCategories()
        {
            var result = _IDocumentRepository.GetDocumentCategories();
            return result;
        }

        public List<MstDocumentLibrary> GetDocumentsByCategory(string category)
        {
            var result = _IDocumentRepository.GetDocumentsByCategory(category);
            return result;
        }

        public List<MstDocumentLibrary> GetDocumentsByCategoryAndFacility(string category, int facilityId)
        {
            var result = _IDocumentRepository.GetDocumentsByCategoryAndFacility(category, facilityId);
            return result;
        }

        public MstDocumentLibrary GetDocumentById(int documentId)
        {
            var result = _IDocumentRepository.GetDocumentById(documentId);
            return result;
        }
    }
}
