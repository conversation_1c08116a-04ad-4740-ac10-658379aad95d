﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IInspectionRepository
    {
        long CreateOrUpdateInspection(MstInspectionDto createInspectionDto);
        int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto);
        int VerifyInspector(VerifyInspectorDto verifyInspectorDto);
        List<InspectionVerifiedDto> GetInspectionsByVerifiedOrNot(int verificationStatus);
        List<MstActionParty> GetActionParties();
        List<MstInspectionCategory> GetInspectionCategory();
        List<InspectionVerifiedDto> GetInspections(int? inspectionId);
    }
}
