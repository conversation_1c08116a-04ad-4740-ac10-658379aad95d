﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IPostRepository
    {
        UserProfile GetUsersByUserId(int userId);
        long CreatePost(CreatePostDto createPostDto);
        (List<PostDetailsDto>, int) GetPosts(PaginationDto pagination, int? userId = null);
        List<MstPostCategory> GetPostCategories();
        int CreateOrUpdateLikes(CreateLikeDto createLikeDto);
        int ClosePost(ClosedPostDto closedPostDto);
        int DeletePost(int postId, int deletedBy);
        int CreateOrUpdateComment(CreateCommentDto createCommentDto);
        int CreateOrUpdateCommentLikes(CommentLikeCreateDto commentLikeDto);
        List<CommentResponseDto> GetCommentsByPostId(int PostId);
        List<LikeReposeDto> GetLikesByPostId(int PostId);
        UserResponseDto UpdateUser(UpdateUserRequestDto dto);
        int AssignPost(AssignPostDto dto);
        int FollowupPost(FollowupDto dto);
        int ArchivedPost(ArchivedDto dto);
        (List<PostDetailsDto>, int) GetAssignedPosts(PaginationDto pagination, int userId);
    }
}
