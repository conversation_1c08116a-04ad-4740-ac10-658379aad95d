﻿using Hsse.Data.Helper;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddDataDI(this IServiceCollection services)
        {
            services.AddTransient<EmailHelper>();
            return services;
        }
    }
}
