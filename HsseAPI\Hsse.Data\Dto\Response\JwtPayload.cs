﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class JwtPayload
    {
        [JsonProperty("userId")]
        public int UserId { get; set; }

        [JsonProperty("userName")]
        public string? Username { get; set; }

        [JsonProperty("userEmail")]
        public string? Email { get; set; }

        [JsonProperty("roleId")]
        public int RoleId { get; set; }

        [JsonProperty("facList")]
        public string? Facilities { get; set; }

        [JsonProperty("language")]
        public string? Language { get; set; }

        [JsonProperty("isAdmin")]
        public bool IsAdmin { get; set; }
    }
    public class LoginApiResponse
    {
        public int Code { get; set; }
        public string Msg { get; set; }
        public string Obj { get; set; } // This contains JSON as a string
    }
    public class SsoDecodedResponse
    {
        public string Jwt { get; set; } = "";
        public string UserName { get; set; } = "";
        public string UserFullName { get; set; } = "";
        public string? UserEmail { get; set; }
        public string? PhotoUrl { get; set; }
        public bool IsAdmin { get; set; }
        public int RoleId { get; set; }
        public int UserId { get; set; }
        public string facList { get; set; }
        public string? Language { get; set; }
    }
}
