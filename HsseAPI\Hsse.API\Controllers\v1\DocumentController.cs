using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [Authorize]
    public class DocumentController : ControllerBase
    {
        private readonly IDocumentService _IDocumentService;
        private readonly ILogger<DocumentController> _logger;

        public DocumentController(IDocumentService documentService, IConfiguration configuration, ILogger<DocumentController> logger)
        {
            _logger = logger;
            _IDocumentService = documentService;
        }

        [HttpGet]
        public IActionResult GetAllDocuments([FromQuery] int? facilityId)
        {
            var response = new ResponseDetails();
            try
            {
                var result = facilityId.HasValue 
                    ? _IDocumentService.GetDocumentsByFacility(facilityId.Value)
                    : _IDocumentService.GetAllDocuments();
                
                response.Status = 1;
                response.Message = "Documents retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving documents";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("categories")]
        public IActionResult GetDocumentCategories()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IDocumentService.GetDocumentCategories();
                response.Status = 1;
                response.Message = "Document categories retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving document categories";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}
