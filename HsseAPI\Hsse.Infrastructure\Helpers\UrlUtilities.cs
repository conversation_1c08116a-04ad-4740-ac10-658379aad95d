﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Helpers
{
    public static class UrlUtilities
    {

        public static string ConstructQueryString(Dictionary<string, object> parameters, bool urlEncode = true)
        {
            if (parameters == null || !parameters.Any())
                return string.Empty;

            var queryBuilder = new StringBuilder();

            foreach (var kvp in parameters)
            {
                if (queryBuilder.Length > 0)
                    queryBuilder.Append("&");

                var key = kvp.Key;
                var value = kvp.Value?.ToString() ?? string.Empty;

                if (urlEncode)
                {
                    key = Uri.EscapeDataString(key);
                    value = Uri.EscapeDataString(value);
                }

                queryBuilder.Append($"{key}={value}");
            }

            return queryBuilder.ToString();
        }
    }
}
