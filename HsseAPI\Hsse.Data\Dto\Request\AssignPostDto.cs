﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Data.Attributes;

namespace Hsse.Data.Dto.Request
{
    public class AssignPostDto
    {
        [Required(ErrorMessage = "Post ID is required")]
        [PositiveNumber(ErrorMessage = "Post ID must be a positive number")]
        public int PostId { get; set; }

        [Required(ErrorMessage = "User ID is required")]
        [PositiveNumber(ErrorMessage = "User ID must be a positive number")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "Assign To is required")]
        [PositiveNumber(ErrorMessage = "Assign To must be a positive number")]
        public int AssignTo { get; set; }
    }

    public class FollowupDto
    {
        [Required(ErrorMessage = "Post ID is required")]
        [PositiveNumber(ErrorMessage = "Post ID must be a positive number")]
        public int PostId { get; set; }

        [Required(ErrorMessage = "User ID is required")]
        [PositiveNumber(ErrorMessage = "User ID must be a positive number")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "Followup is required")]
        [Range(0, 1, ErrorMessage = "Followup must be 0 or 1")]
        public int Followup { get; set; }
    }

    public class ArchivedDto
    {
        [Required(ErrorMessage = "Post ID is required")]
        [PositiveNumber(ErrorMessage = "Post ID must be a positive number")]
        public int PostId { get; set; }

        [Required(ErrorMessage = "User ID is required")]
        [PositiveNumber(ErrorMessage = "User ID must be a positive number")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "Archived is required")]
        [Range(0, 1, ErrorMessage = "Archived must be 0 or 1")]
        public int Archived { get; set; }
    }
}
