﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class UserResponseDto
    {
        public int UserId { get; set; }

        public string Username { get; set; } = null!;

        public string FirstName { get; set; } = null!;

        public string LastName { get; set; } = null!;

        public string Email { get; set; } = null!;

        public string? ProfileImageUrl { get; set; }

        public string? Bio { get; set; }

        public int? PrimaryFacilityId { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? LastLogin { get; set; }

        public bool? IsActive { get; set; }

        public int? SsoUserId { get; set; }

        public bool IsSsoUser { get; set; }

        public string? EmployeeCode { get; set; }

        public string? ContactNumber { get; set; }

        public int? LanguageId { get; set; }
    }
}
