﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class PostDetailsDto
    {
        public int PostID { get; set; }
        public int UserID { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? PostType { get; set; }
        public string? Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public string? RequiresFollowup { get; set; }
        public string? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int CommentsCount { get; set; }
        public int LikesCount { get; set; }
        public string MediaUrl { get; set; }
        public string AfterMediaUrl { get; set; }
    }

}
