﻿using Hsse.Data.Dto.Request;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace Hsse.Application.Helper
{
    public class EncryptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly EncryptionSettings _settings;

        public EncryptionMiddleware(RequestDelegate next, IOptions<EncryptionSettings> options)
        {
            _next = next;
            _settings = options.Value;
        }

        public async Task InvokeAsync(HttpContext context, EncryptionHelper encryptionHelper)
        {
            // 🔐 Validate API Key
            if (!context.Request.Headers.TryGetValue("X-ApiKey", out var apiKey))
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("Missing API Key");
                return;
            }

            bool isEncryptedMode = apiKey == _settings.EncryptApiKey;
            bool isDecryptedMode = apiKey == _settings.DecryptApiKey;

            if (!isEncryptedMode && !isDecryptedMode)
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("Invalid API Key");
                return;
            }

            // 🔓 Decrypt query parameters if encrypted mode
            if (isEncryptedMode && context.Request.Query.Count > 0)
            {
                var newQueryBuilder = new QueryBuilder();
                foreach (var pair in context.Request.Query)
                {
                    try
                    {
                        var decryptedValue = encryptionHelper.Decrypt(pair.Value!);
                        newQueryBuilder.Add(pair.Key, decryptedValue);
                    }
                    catch
                    {
                        //newQueryBuilder.Add(pair.Key, (string)pair.Value!); // fallback
                        context.Response.StatusCode = StatusCodes.Status400BadRequest;
                        await context.Response.WriteAsync("Invalid encrypted request body.");
                        return;
                    }
                }
                context.Request.QueryString = newQueryBuilder.ToQueryString();
            }

            // 🔓 Decrypt request body if in encrypted mode
            if (isEncryptedMode && context.Request.ContentLength > 0)
            {
                context.Request.EnableBuffering();
                using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
                var encryptedBody = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;

                try
                {
                    var decryptedBody = encryptionHelper.Decrypt(encryptedBody);
                    var bodyBytes = Encoding.UTF8.GetBytes(decryptedBody);
                    context.Request.Body = new MemoryStream(bodyBytes);
                }
                catch
                {
                    context.Response.StatusCode = StatusCodes.Status400BadRequest;
                    await context.Response.WriteAsync("Invalid encrypted request body.");
                    return;
                }
            }

            // 🔄 Intercept and process the response
            var originalBodyStream = context.Response.Body;
            using var tempStream = new MemoryStream();
            context.Response.Body = tempStream;

            await _next(context); // Controller executes here

            tempStream.Seek(0, SeekOrigin.Begin);
            var responseBody = await new StreamReader(tempStream).ReadToEndAsync();

            string finalResponse = isEncryptedMode
                ? encryptionHelper.Encrypt(responseBody)
                : responseBody;

            var responseBytes = Encoding.UTF8.GetBytes(finalResponse);
            context.Response.Body = originalBodyStream;
            context.Response.ContentLength = responseBytes.Length;
            context.Response.ContentType = "application/json";

            await context.Response.Body.WriteAsync(responseBytes, 0, responseBytes.Length);
        }

    }
}
