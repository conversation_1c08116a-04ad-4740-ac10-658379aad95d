﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstUserRolesConfig
{
    public int UserRoleConfigId { get; set; }

    public int? UserId { get; set; }

    public int? RoleId { get; set; }

    public int? FacilityId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public virtual MstFacility? Facility { get; set; }

    public virtual MstUsersRole? Role { get; set; }

    public virtual MstUser? User { get; set; }
}
