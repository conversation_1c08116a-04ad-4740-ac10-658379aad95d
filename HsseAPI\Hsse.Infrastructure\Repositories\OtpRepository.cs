﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Infrastructure.Helpers;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Win32.SafeHandles;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class OtpRepository : IOtpRepository, IDisposable
    {
        private readonly MasterDBContext _context;
        private readonly ILogger<OtpRepository> _logger;
        private readonly IConfiguration _configuration;
        private bool disposed = false;
        private SafeHandle handle = new SafeFileHandle(IntPtr.Zero, true);

        // Configuration constants
        private const int OTP_EXPIRY_MINUTES = 5;
        private const int MAX_RESEND_ATTEMPTS = 3;
        private const int OTP_LENGTH = 6;

        public OtpRepository(MasterDBContext context, ILogger<OtpRepository> logger, IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _configuration = configuration;

            // Set logger for SMS service provider
            SMSServiceProvider.SetLogger(_logger);
        }

        public async Task<OtpTriggerResponseDto> TriggerOtpAsync(string empNameId)
        {
            _logger.LogInformation($"TriggerOtp started for empNameId: {empNameId}");
            var response = new OtpTriggerResponseDto();
            try
            {
                var employee = await _context.MstUsers.FirstOrDefaultAsync(x => x.EmployeeCode == empNameId && x.IsActive != false);

                if (employee == null)
                {
                    response.Status = 0;
                    response.Message = "Employee details do not exist.";
                    _logger.LogWarning($"Employee details not found for empNameId: {empNameId}");
                    return response;
                }

                var ret = await SendAndGenerateOTPResponse(employee.UserId, employee);
                response.Status = 1;
                response.Message = "OTP sent successfully";
                response.ReferenceId = ret.ReferenceId;
                response.ExpiryTime = ret.ExpiresAtDate;
                response.ResendAttempts = ret.RetryCount;
                _logger.LogInformation($"OTP sent successfully for empNameId: {empNameId}, employeeId: {employee.UserId}");

                return response;
            }
            catch (Exception ex)
            {
                response.Status = 0;
                response.Message = "Error occurred while sending the OTP. Details: " + ex.Message;
                _logger.LogError(ex, $"Error occurred while sending OTP for empNameId: {empNameId}");
                return response;
            }
        }

        public async Task<OtpResendResponseDto> ResendOtpAsync(string empNameId, Guid referenceId)
        {
            var response = new OtpResendResponseDto();
            MstUser? employee = null;

            try
            {
                employee = await _context.MstUsers.FirstOrDefaultAsync(x => x.EmployeeCode == empNameId && x.IsActive != false);

                if (employee == null)
                {
                    response.Status = 0;
                    response.Message = "Employee details do not exist.";
                    _logger.LogWarning($"Employee details not found for empNameId: {empNameId}");
                    return response;
                }

                var otpResponse = await SendAndGenerateOTPResponse(employee.UserId, employee);
                var newRetryCount = OtpRetryCountUpdate(referenceId);
                otpResponse.RetryCount = newRetryCount;

                response.Status = 1;
                response.Message = "OTP sent successfully";
                response.ReferenceId = otpResponse.ReferenceId;
                response.ExpiryTime = otpResponse.ExpiresAtDate;
                response.ResendAttempts = otpResponse.RetryCount;
                response.MaxResendAttempts = MAX_RESEND_ATTEMPTS;
                _logger.LogInformation($"OTP sent successfully for empNameId: {empNameId}, employeeId: {employee.UserId}");
            }
            catch (Exception ex)
            {
                response.Status = 0;
                response.Message = $"Error occurred while resending OTP. Details: {ex.Message}";
                _logger.LogError(ex, $"Error occurred while resending OTP for empNameId: {empNameId}");
            }

            return response;
        }

        public OtpValidationResponseDto ValidateOtp(string empNameId, Guid referenceId, string otp)
        {
            var response = new OtpValidationResponseDto();
            MstUser? user = null;

            try
            {
                string environment = _configuration["Environment"] ?? "PROD";
                string testEmpNameId = _configuration["TestEmpNameID"] ?? "";
                MstOtpValidation? otpValidation;

                user = _context.MstUsers.FirstOrDefault(x => x.EmployeeCode == empNameId);
                otpValidation = _context.MstOtpValidations.FirstOrDefault(x => x.Id == referenceId);

                if (user == null)
                {
                    return GenerateErrorResponse(response, "Employee not found.", referenceId, 0, "Employee not found for empNameId.");
                }

                if (environment == "DEV" || (user != null && user.EmployeeCode?.ToUpper() == testEmpNameId.ToUpper()))
                {
                    return GenerateSuccessResponse(response, otpValidation?.RetryCount ?? 0, "OTP validation successful.");
                }

                if (otpValidation == null)
                {
                    return GenerateErrorResponse(response, "Invalid request.", referenceId, user.UserId, "Invalid OTP validation request.");
                }

                var hashData = ComputeOtpHash(user.UserId, otp);

                if (otp.Length != OTP_LENGTH)
                {
                    return GenerateErrorResponse(response, $"The OTP length should be {OTP_LENGTH}.", referenceId, user.UserId, "Invalid OTP length.");
                }

                if (hashData != otpValidation.HashData)
                {
                    return GenerateErrorResponse(response, "The OTP entered is invalid.", referenceId, user.UserId, "Invalid OTP entered.");
                }

                if (otpValidation.RetryCount <= 0)
                {
                    return GenerateErrorResponse(response, "You have exceeded the number of allowed OTP attempts.", referenceId, user.UserId, "Exceeded OTP retry attempts.");
                }

                var createdDate = otpValidation.CreatedDate.ToUniversalTime();
                var expireDate = createdDate.AddMinutes(OTP_EXPIRY_MINUTES);
                bool isSuccess = DateTime.UtcNow < expireDate;

                response.Status = isSuccess ? 1 : 0;
                response.Message = isSuccess ? "OTP validation successful." : "OTP validation failed.";
                response.IsValid = isSuccess;
                response.ValidatedAt = isSuccess ? DateTime.Now : null;
                response.RemainingAttempts = otpValidation.RetryCount;

                if (!isSuccess)
                {
                    response.Message = "The OTP has expired. Please try again.";
                }

                _logger.LogInformation($"OTP validation {(isSuccess ? "successful" : "failed")} for EmpNameId: {empNameId}, User ID: {user.UserId}");
                return response;
            }
            catch (Exception ex)
            {
                return GenerateExceptionResponse(response, ex, user?.UserId ?? 0);
            }
        }

        public async Task<OtpValidationResponseDto> ValidateOtpAsync(string empNameId, Guid referenceId, string otp)
        {
            return await Task.FromResult(ValidateOtp(empNameId, referenceId, otp));
        }



        // Synchronous versions for backward compatibility
        public OtpTriggerResponseDto TriggerOtp(string empNameId)
        {
            return TriggerOtpAsync(empNameId).GetAwaiter().GetResult();
        }

        public OtpResendResponseDto ResendOtp(string empNameId, Guid referenceId)
        {
            return ResendOtpAsync(empNameId, referenceId).GetAwaiter().GetResult();
        }

        public Guid OtpSaveAsync(int userId, int retryCount, string hashData)
        {
            _logger.LogInformation($"Saving OTP for userId: {userId} with retryCount: {retryCount}");

            var otpItem = new MstOtpValidation
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                HashData = hashData,
                RetryCount = retryCount,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
            };
            _context.MstOtpValidations.Add(otpItem);
            _context.SaveChanges();
            _logger.LogInformation($"OTP saved for userId: {userId} with referenceId: {otpItem.Id}");
            return otpItem.Id;
        }
        public int OtpRetryCountUpdate(Guid referenceId)
        {
            _logger.LogInformation($"Updating OTP retry count for referenceId: {referenceId}");

            var otpValidation = _context.MstOtpValidations.FirstOrDefault(x => x.Id == referenceId);
            if (otpValidation == null || otpValidation.RetryCount == 0)
            {
                var errorMsg = "You have exceeded the number of allowed OTP attempts.";
                _logger.LogWarning(errorMsg);
                throw new Exception(errorMsg);
            }

            otpValidation.RetryCount -= 1;
            _context.SaveChanges();
            _logger.LogInformation($"Updated retry count for referenceId: {referenceId} to {otpValidation.RetryCount}");
            return otpValidation.RetryCount;
        }

        private async Task<OtpResponse> SendAndGenerateOTPResponse(int userId, MstUser employee)
        {
            _logger.LogInformation($"Sending OTP to userId: {userId}");
            var isSendOTP = true;
            var smsResponse = true;
            string environment = _configuration["Environment"] ?? "PROD";
            string testEmpNameId = _configuration["TestEmpNameID"] ?? "";

            if (environment == "DEV" || (employee.EmployeeCode?.ToUpper() == testEmpNameId.ToUpper()))
                isSendOTP = false;

            var otpValue = GenerateNewOtp(isSendOTP);
            var hashData = ComputeOtpHash(userId, otpValue);

            var otpData = new OtpData
            {
                OTP = otpValue,
                MessageId = _configuration["SMS:MessageId"] ?? OtpConfig.MessageId,
                Pwd = _configuration["SMS:Password"] ?? OtpConfig.Pwd,
                Parameter = _configuration["SMS:Parameter"] ?? OtpConfig.Parameter,
                Provider = _configuration["SMS:ProviderUrl"] ?? OtpConfig.Provider,
                PhoneNumber = employee.ContactNumber ?? "",
            };

            if (isSendOTP)
            {
                smsResponse = await SMSServiceProvider.SendOtpAsync(otpData);
            }

            if (!smsResponse)
            {
                var errorMsg = "Send SMS failed.";
                _logger.LogError(errorMsg);
                throw new Exception(errorMsg);
            }

            var refId = OtpSaveAsync(userId, MAX_RESEND_ATTEMPTS, hashData);
            var expDate = DateTime.UtcNow.AddMinutes(OTP_EXPIRY_MINUTES);

            var ret = new OtpResponse
            {
                ReferenceId = refId,
                RetryCount = MAX_RESEND_ATTEMPTS,
                OtpLength = OTP_LENGTH,
                ExpiresAtDate = expDate,
                ExpiresAt = new DateTimeOffset(expDate).ToUnixTimeSeconds(),
            };

            _logger.LogInformation($"OTP response generated for userId: {userId} with referenceId: {refId}");
            return ret;
        }
        private string GenerateNewOtp(bool isSendOTP)
        {
            _logger.LogDebug($"Generating new OTP. Send OTP flag: {isSendOTP}");
            var len = OTP_LENGTH;
            var min = (int)Math.Pow(10, len - 1) + 1;
            var max = (int)Math.Pow(10, len) - 1;
            var random = new Random();
            var otp = random.Next(min, max);

            if (!isSendOTP)
            {
                otp = 123456; // Default OTP for testing
            }

            _logger.LogDebug($"Generated OTP: {otp}");
            return otp.ToString();
        }

        private string ComputeOtpHash(int userId, string otp)
        {
            _logger.LogDebug($"Computing OTP hash for userId: {userId}");
            var str = GenerateOtpHashInput(userId, otp);
            var dataBytes = Encoding.UTF8.GetBytes(str);

            using (var alg = GetHasher(otp))
            {
                var hashBytes = alg.ComputeHash(dataBytes);
                var strHash = Convert.ToBase64String(hashBytes);
                _logger.LogDebug($"Computed OTP hash: {strHash}");
                return strHash;
            }
        }
        private HMAC GetHasher(string otp)
        {
            var bytes = Encoding.UTF8.GetBytes(otp);
            var ret = new HMACSHA512(bytes);
            return ret;
        }
        private static string GenerateOtpHashInput(int userId, string otp)
        {
            return $"{userId}|{otp}";
        }

        private OtpValidationResponseDto GenerateErrorResponse(OtpValidationResponseDto response, string message, Guid referenceId, int userId, string logMessage)
        {
            response.Status = 0;
            response.Message = message;
            response.IsValid = false;
            _logger.LogWarning($"{logMessage} Reference ID: {referenceId}, User ID: {userId}");
            return response;
        }

        private OtpValidationResponseDto GenerateSuccessResponse(OtpValidationResponseDto response, int retryCount, string message)
        {
            response.Status = 1;
            response.Message = message;
            response.IsValid = true;
            response.RemainingAttempts = retryCount;
            return response;
        }

        private OtpValidationResponseDto GenerateExceptionResponse(OtpValidationResponseDto response, Exception ex, int userId)
        {
            response.Status = 0;
            response.Message = $"Error occurred while validating OTP. Details: {ex.Message}";
            response.IsValid = false;
            _logger.LogError(ex, $"Error occurred while validating OTP for User ID: {userId}");
            return response;
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the resources used by the repository.
        /// </summary>
        /// <param name="disposing">Indicates if managed resources should be disposed.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            if (disposing)
            {
                handle.Dispose();
                // Free any other managed objects here.
            }

            disposed = true;
        }
    }
}
