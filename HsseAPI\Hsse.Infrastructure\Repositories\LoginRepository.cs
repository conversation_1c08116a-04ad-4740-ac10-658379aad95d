﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class LoginRepository : ILoginRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;


        public LoginRepository(IConfiguration configuration, HttpClient httpClient, MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public LoginResponseDto ValidateUser(LoginRequestDto loginRequestDto)
        {
            try
            {
                var user = _MasterDBContext.MstUsers
                    .Where(u => u.Email == loginRequestDto.employeeEmail || u.EmployeeCode == loginRequestDto.employeeId)
                    .FirstOrDefault();

                if (user != null)
                {
                    if (loginRequestDto.langId != null)
                    {
                        user.LanguageId = loginRequestDto.langId;
                        _MasterDBContext.Entry(user).State = EntityState.Modified;
                        _MasterDBContext.SaveChanges();
                    }
                    var userRoleConfig = _MasterDBContext.MstUserRolesConfigs
                        .Where(x => x.UserId == user.UserId)
                        .FirstOrDefault();

                    var roleName = _MasterDBContext.MstUsersRoles
                        .Where(x => x.RoleId == userRoleConfig.RoleId)
                        .Select(x => x.RoleName)
                        .FirstOrDefault();

                    var facilityName = _MasterDBContext.MstFacilities
                        .Where(x => x.FacilityId == userRoleConfig.FacilityId)
                        .Select(x => x.FacilityName)
                        .FirstOrDefault();

                    var result = new LoginResponseDto
                    {
                        UserId = user.UserId,
                        UserName = user.Username,
                        Name = $"{user.FirstName} {user.LastName}",
                        EmployeeId = user.EmployeeCode,
                        RoleId = userRoleConfig.RoleId,
                        RoleName = roleName,
                        facilityId = userRoleConfig.FacilityId,
                        FacilityName = facilityName,
                        ProfileImageUrl = user.ProfileImageUrl,
                        LastLogin = user.LastLogin?.ToString("yyyy-MM-dd HH:mm:ss"),
                        Email = user.Email,
                    };
                    return result;
                }
                return null;

            }
            catch (Exception ex)
            {
                throw ex;
            }

        }
        public List<MstLanguage> GetLanguages()
        {
            var result = _MasterDBContext.MstLanguages.ToList();
            return result;
        }
        public DeviceRequestDto AppInformation(DeviceRequestDto deviceDetail)
        {
            // Try to find existing record for this UserId
           var  mstDeviceDetail = _MasterDBContext.MstDeviceDetails
                .FirstOrDefault(x => x.UserId == deviceDetail.UserId);

            if (mstDeviceDetail != null)
            {
                // UPDATE existing record
                mstDeviceDetail.FacilityId = deviceDetail.FacilityId;
                mstDeviceDetail.Osname = deviceDetail.Osname;
                mstDeviceDetail.AppVersion = deviceDetail.AppVersion;
                mstDeviceDetail.Datetime = DateTime.Now;
                mstDeviceDetail.Fcm = deviceDetail.Fcm;
                mstDeviceDetail.Location = deviceDetail.Location;
                mstDeviceDetail.DeviceUniqueId = deviceDetail.DeviceUniqueId;
                mstDeviceDetail.Lat = deviceDetail.Lat;
                mstDeviceDetail.Long = deviceDetail.Long;

                _MasterDBContext.Entry(mstDeviceDetail).State = EntityState.Modified;
            }
            else
            {
                // CREATE new record if not found
                mstDeviceDetail = new MstDeviceDetail
                {
                    FacilityId = deviceDetail.FacilityId,
                    Osname = deviceDetail.Osname,
                    AppVersion = deviceDetail.AppVersion,
                    Datetime = DateTime.Now,
                    Fcm = deviceDetail.Fcm,
                    Location = deviceDetail.Location,
                    DeviceUniqueId = deviceDetail.DeviceUniqueId,
                    Lat = deviceDetail.Lat,
                    Long = deviceDetail.Long,
                    UserId = deviceDetail.UserId ?? 0,
                    CreatedAt = DateTime.Now
                };

                _MasterDBContext.MstDeviceDetails.Add(mstDeviceDetail);
            }

            _MasterDBContext.SaveChanges();

            // Return the saved values (including generated ID)
            return new DeviceRequestDto
            {
                Id = mstDeviceDetail.Id,
                FacilityId = mstDeviceDetail.FacilityId,
                Osname = mstDeviceDetail.Osname,
                AppVersion = mstDeviceDetail.AppVersion,
                Fcm = mstDeviceDetail.Fcm,
                Location = mstDeviceDetail.Location,
                DeviceUniqueId = mstDeviceDetail.DeviceUniqueId,
                Lat = mstDeviceDetail.Lat,
                Long = mstDeviceDetail.Long,
                UserId = mstDeviceDetail.UserId
            };

        }


    }
}

