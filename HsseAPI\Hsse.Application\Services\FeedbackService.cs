using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class FeedbackService : IFeedbackService
    {
        private readonly IFeedbackRepository _IFeedbackRepository;

        public FeedbackService(IFeedbackRepository feedbackRepository)
        {
            _IFeedbackRepository = feedbackRepository;
        }
        public int CreateOrUpdateFeedback(FeedbackRequestDto dto)
        {
            var result = _IFeedbackRepository.CreateOrUpdateFeedback(dto);
            return result;
        }
    }
}
