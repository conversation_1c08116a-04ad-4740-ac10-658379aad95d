using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class DocumentRepository : IDocumentRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public DocumentRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public List<MstDocumentLibrary> GetAllDocuments()
        {
            return _MasterDBContext.MstDocumentLibraries
                .Where(d => !d.IsDeleted)
                .OrderByDescending(d => d.Date)
                .ToList();
        }

        public List<MstDocumentLibrary> GetDocumentsByFacility(int facilityId)
        {
            return _MasterDBContext.MstDocumentLibraries
                .Where(d => !d.IsDeleted)
                .OrderByDescending(d => d.Date)
                .ToList();
        }

        public List<string> GetDocumentCategories()
        {
            return _MasterDBContext.MstDocumentLibraries
                .Where(d => !d.IsDeleted && !string.IsNullOrEmpty(d.Category))
                .Select(d => d.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToList();
        }

        public List<MstDocumentLibrary> GetDocumentsByCategory(string category)
        {
            return _MasterDBContext.MstDocumentLibraries
                .Where(d => !d.IsDeleted && d.Category == category)
                .OrderByDescending(d => d.Date)
                .ToList();
        }

        public List<MstDocumentLibrary> GetDocumentsByCategoryAndFacility(string category, int facilityId)
        {
            return _MasterDBContext.MstDocumentLibraries
                .Where(d => !d.IsDeleted && d.Category == category)
                .OrderByDescending(d => d.Date)
                .ToList();
        }

        public MstDocumentLibrary GetDocumentById(int documentId)
        {
            return _MasterDBContext.MstDocumentLibraries
                .FirstOrDefault(d => d.DocumentId == documentId && !d.IsDeleted);
        }
    }
}
