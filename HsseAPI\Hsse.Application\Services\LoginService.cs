﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using Hsse.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class LoginService : ILoginService
    {
        private readonly ILoginRepository _ILoginRepository;
        public LoginService(ILoginRepository loginRepository)
        {
            _ILoginRepository = loginRepository;
        }
        public LoginResponseDto ValidateUser(LoginRequestDto loginRequestDto)
        {
            var result = _ILoginRepository.ValidateUser(loginRequestDto);
            return result;
        }
        public List<MstLanguage> GetLanguages()
        {
            var result = _ILoginRepository.GetLanguages();
            return result;
        }
        public DeviceRequestDto AppInformation(DeviceRequestDto deviceDetail)
        {
            var result = _ILoginRepository.AppInformation(deviceDetail);
            return result;
        }
    }
}
