﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class OtpController : ControllerBase
    {
        private readonly IOtpService _otpService;
        private readonly ILogger<OtpController> _logger;

        public OtpController(IOtpService otpService, ILogger<OtpController> logger)
        {
            _otpService = otpService;
            _logger = logger;
        }

        [AllowAnonymous]
        [HttpPost("TriggerOtp")]
        public async Task<IActionResult> TriggerOtp([FromBody] OtpTriggeredRQ request)
        {
            var response = new ResponseDetails();

            try
            {
                if (string.IsNullOrWhiteSpace(request.EmpNameId))
                {
                    response.Status = 0;
                    response.Message = "Employee Name ID is required";
                    return BadRequest(response);
                }

                var result = await _otpService.TriggerOtpAsync(request);

                response.Status = result.Status;
                response.Message = result.Message;
                response.Result = new
                {
                    result.ReferenceId,
                    result.ExpiryTime,
                    result.ResendAttempts
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError("Method Name: {MethodName}, Error: {ErrorMessage}", method ?? "", ex.Message);

                response.Status = 0;
                response.Message = "Something went wrong. Please try again later.";
                return StatusCode(500, response);
            }
        }

        [AllowAnonymous]
        [HttpPost("ResendOtp")]
        public async Task<IActionResult> ResendOtp([FromBody] ResendOtpRQ request)
        {
            var response = new ResponseDetails();

            try
            {
                if (string.IsNullOrWhiteSpace(request.EmpNameId))
                {
                    response.Status = 0;
                    response.Message = "Employee Name ID is required";
                    return BadRequest(response);
                }

                if (request.ReferenceId == Guid.Empty)
                {
                    response.Status = 0;
                    response.Message = "Invalid reference ID";
                    return BadRequest(response);
                }

                var result = await _otpService.ResendOtpAsync(request);

                response.Status = result.Status;
                response.Message = result.Message;
                response.Result = new
                {
                    result.ReferenceId,
                    result.ExpiryTime,
                    result.ResendAttempts,
                    result.MaxResendAttempts
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError("Method Name: {MethodName}, Error: {ErrorMessage}", method ?? "", ex.Message);

                response.Status = 0;
                response.Message = "Something went wrong. Please try again later.";
                return StatusCode(500, response);
            }
        }

        [AllowAnonymous]
        [HttpPost("SubmitOtp")]
        public async Task<IActionResult> SubmitOtp([FromBody] OtpSubmitRQ request)
        {
            var response = new ResponseDetails();

            try
            {
                if (string.IsNullOrWhiteSpace(request.EmpNameId))
                {
                    response.Status = 0;
                    response.Message = "Employee Name ID is required";
                    return BadRequest(response);
                }

                if (request.ReferenceId == Guid.Empty)
                {
                    response.Status = 0;
                    response.Message = "Invalid reference ID";
                    return BadRequest(response);
                }

                if (string.IsNullOrWhiteSpace(request.Otp))
                {
                    response.Status = 0;
                    response.Message = "OTP is required";
                    return BadRequest(response);
                }

                var result = await _otpService.ValidateOtpAsync(request);

                response.Status = result.Status;
                response.Message = result.Message;
                response.Result = new
                {
                    result.IsValid,
                    result.ValidatedAt,
                    result.RemainingAttempts
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError("Method Name: {MethodName}, Error: {ErrorMessage}", method ?? "", ex.Message);

                response.Status = 0;
                response.Message = "Something went wrong. Please try again later.";
                return StatusCode(500, response);
            }
        }


    }
}
