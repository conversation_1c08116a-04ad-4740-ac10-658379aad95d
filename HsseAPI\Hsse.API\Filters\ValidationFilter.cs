using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Hsse.Data.Dto.Response;

namespace Hsse.API.Filters
{
    public class ValidationFilter : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (!context.ModelState.IsValid)
            {
                var validationResponse = new ValidationErrorResponse();
                
                foreach (var modelError in context.ModelState)
                {
                    foreach (var error in modelError.Value.Errors)
                    {
                        validationResponse.Errors.Add(new ValidationError
                        {
                            Field = modelError.Key,
                            Message = error.ErrorMessage,
                            AttemptedValue = modelError.Value.AttemptedValue
                        });
                    }
                }

                context.Result = new BadRequestObjectResult(validationResponse);
            }
        }
    }
}
