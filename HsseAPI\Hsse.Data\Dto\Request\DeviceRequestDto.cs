﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Data.Attributes;

namespace Hsse.Data.Dto.Request
{
    public class DeviceRequestDto
    {
        public int Id { get; set; }

        [PositiveNumber(ErrorMessage = "Facility ID must be a positive number")]
        public int? FacilityId { get; set; }

        [StringLength(50, ErrorMessage = "OS name cannot exceed 50 characters")]
        public string? Osname { get; set; }

        [StringLength(20, ErrorMessage = "App version cannot exceed 20 characters")]
        [RegularExpression(@"^\d+\.\d+(\.\d+)?$", ErrorMessage = "App version must be in format x.y or x.y.z")]
        public string? AppVersion { get; set; }

        [StringLength(500, ErrorMessage = "FCM token cannot exceed 500 characters")]
        public string? Fcm { get; set; }

        [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
        public string? Location { get; set; }

        [StringLength(100, ErrorMessage = "Device unique ID cannot exceed 100 characters")]
        public string? DeviceUniqueId { get; set; }

        [RegularExpression(@"^-?([1-8]?[1-9]|[1-9]0)\.{1}\d{1,6}$", ErrorMessage = "Latitude must be a valid coordinate")]
        public string? Lat { get; set; }

        [RegularExpression(@"^-?((1[0-7]|[1-9])?[0-9]\.{1}\d{1,6}$|^180\.{1}0{1,6})$", ErrorMessage = "Longitude must be a valid coordinate")]
        public string? Long { get; set; }

        [PositiveNumber(ErrorMessage = "User ID must be a positive number")]
        public int? UserId { get; set; }
    }
}
