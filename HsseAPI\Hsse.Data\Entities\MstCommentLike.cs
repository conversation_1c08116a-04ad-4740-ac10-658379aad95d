﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstCommentLike
{
    public int CommentLikeId { get; set; }

    public int? CommentId { get; set; }

    public int? UserId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public bool IsLiked { get; set; }

    public virtual MstPostComment? Comment { get; set; }

    public virtual MstUser? User { get; set; }
}
