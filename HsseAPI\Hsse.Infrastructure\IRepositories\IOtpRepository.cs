﻿using Hsse.Data.Dto.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IOtpRepository
    {
        Task<OtpTriggerResponseDto> TriggerOtpAsync(string empNameId);
        Task<OtpResendResponseDto> ResendOtpAsync(string empNameId, Guid referenceId);
        Task<OtpValidationResponseDto> ValidateOtpAsync(string empNameId, Guid referenceId, string otp);

        // Synchronous versions for backward compatibility
        OtpTriggerResponseDto TriggerOtp(string empNameId);
        OtpResendResponseDto ResendOtp(string empNameId, Guid referenceId);
        OtpValidationResponseDto ValidateOtp(string empNameId, Guid referenceId, string otp);
    }
}
