using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IEventRepository
    {
        long CreateOrUpdateEvent(CreateEventDto createEventDto);
        List<GetEventResponseDto> GetEvents(int userId);
        int CreateOrUpdateEventResponse(EventResponseDto dto);
    }
}
