using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Data.Attributes;

namespace Hsse.Data.Dto.Request
{
    public class CreateAnnouncementDto
    {
        public int AnnouncementsId { get; set; }

        [Required(ErrorMessage = "Title is required")]
        [NotEmpty(ErrorMessage = "Title cannot be empty")]
        [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
        public string Title { get; set; }

        [Required(ErrorMessage = "Description is required")]
        [NotEmpty(ErrorMessage = "Description cannot be empty")]
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
        public string Description { get; set; }

        [Range(0, 1, ErrorMessage = "Status must be 0 or 1")]
        public int? Status { get; set; }

        [PositiveNumber(ErrorMessage = "Created By must be a positive number")]
        public int? CreatedBy { get; set; }

        [PositiveNumber(ErrorMessage = "Modified By must be a positive number")]
        public int? ModifiedBy { get; set; }

        [Base64Validation(ErrorMessage = "Image must be a valid Base64 string")]
        public string ImageBase64 { get; set; }

        [StringLength(255, ErrorMessage = "Filename cannot exceed 255 characters")]
        public string Filename { get; set; }

        [FutureDate(ErrorMessage = "Schedule date must be in the future")]
        public DateTime? ScheduleAt { get; set; }

        [FutureDate(ErrorMessage = "Expiry date must be in the future")]
        public DateTime? ExpiryAt { get; set; }

        [PositiveNumber(ErrorMessage = "Facility ID must be a positive number")]
        public int? FacilityID { get; set; }

        [PositiveNumber(ErrorMessage = "Category ID must be a positive number")]
        public int? CategoryId { get; set; }

        public List<int> ReceiverUserIds { get; set; } = new List<int>();
        public List<int> ReceiverGroupIds { get; set; } = new List<int>();
        public List<CreateAnnouncementDocumentDto> Documents { get; set; } = new List<CreateAnnouncementDocumentDto>();
    }

    public class CreateAnnouncementDocumentDto
    {
        public int DocumentID { get; set; }

        [Required(ErrorMessage = "Document name is required")]
        [NotEmpty(ErrorMessage = "Document name cannot be empty")]
        [StringLength(255, ErrorMessage = "Document name cannot exceed 255 characters")]
        public string DocumentName { get; set; }

        [Required(ErrorMessage = "Document file is required")]
        [NotEmpty(ErrorMessage = "Document file cannot be empty")]
        [Base64Validation(ErrorMessage = "Document file must be a valid Base64 string")]
        public string DocumentFile { get; set; }
    }
}
